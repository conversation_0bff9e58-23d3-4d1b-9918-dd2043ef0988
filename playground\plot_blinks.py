import os
import pandas as pd
import matplotlib.pyplot as plt
from tools.proc import get_blinks_per_marker
import numpy as np

def plot_blink_frequencies(file_path, aggregate_data=None, plot_individual=False):
    # Read the CSV file
    df = pd.read_csv(file_path, sep=";")
    
    # Extract required data
    markers = df["Marker"].to_numpy()
    x = df["Screen_X_filtered"].to_numpy()
    y = df["Screen_Y_filtered"].to_numpy()
    t = df["Timestamp"].to_numpy()
    
    # Get blink frequencies with different minimum durations
    blinks_100ms = get_blinks_per_marker(markers, x, y, t, max_blink_duration=0.4, min_blink_duration=0.1)
    blinks_50ms = get_blinks_per_marker(markers, x, y, t, max_blink_duration=0.4, min_blink_duration=0.05)
    blinks_5ms = get_blinks_per_marker(markers, x, y, t, max_blink_duration=0.4, min_blink_duration=0.005)
    blinks_0ms = get_blinks_per_marker(markers, x, y, t, max_blink_duration=0.4, min_blink_duration=0.0)
    
    # If aggregate_data is provided, add this subject's data to it
    if aggregate_data is not None:
        for marker in blinks_100ms.keys():
            if marker not in aggregate_data:
                aggregate_data[marker] = {
                    '100ms': [], '50ms': [], '5ms': [], '0ms': []
                }
            aggregate_data[marker]['100ms'].append(blinks_100ms[marker] * 60)
            aggregate_data[marker]['50ms'].append(blinks_50ms[marker] * 60)
            aggregate_data[marker]['5ms'].append(blinks_5ms[marker] * 60)
            aggregate_data[marker]['0ms'].append(blinks_0ms[marker] * 60)
    if not plot_individual:
        return

    # Create lists for plotting
    marker_nums = sorted([m for m in blinks_100ms.keys()])
    freq_100ms = [blinks_100ms[m] * 60 for m in marker_nums]
    freq_50ms = [blinks_50ms[m] * 60 for m in marker_nums]
    freq_5ms = [blinks_5ms[m] * 60 for m in marker_nums]
    freq_0ms = [blinks_0ms[m] * 60 for m in marker_nums]
    
    # Create the plot
    plt.figure(figsize=(15, 8))
    
    # Plot all frequencies
    x_pos = np.arange(len(marker_nums))
    width = 0.2
    
    plt.bar(x_pos - width*1.5, freq_100ms, width, label='NaN gaps 0.10s-0.4s', color='blue', alpha=0.6)
    plt.bar(x_pos - width*0.5, freq_50ms, width, label='NaN gaps 0.05s-0.4s', color='red', alpha=0.6)
    plt.bar(x_pos + width*0.5, freq_5ms, width, label='NaN gaps 0.005s-0.4s', color='green', alpha=0.6)
    plt.bar(x_pos + width*1.5, freq_0ms, width, label='NaN gaps 0.0s-0.4s', color='purple', alpha=0.6)
    
    plt.xlabel('Marker')
    plt.ylabel('Blinks per Minute')
    plt.title(f'Blink Frequencies by Marker\n{os.path.basename(file_path)}')
    plt.xticks(x_pos, marker_nums, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save individual plot
    output_dir = "results/blink_plots"
    os.makedirs(output_dir, exist_ok=True)
    output_file = os.path.join(output_dir, f"{os.path.basename(file_path)}_blinks.png")
    plt.savefig(output_file)
    plt.close()

def plot_aggregate_blinks(aggregate_data):
    # Calculate means and standard errors for each marker and condition
    marker_nums = sorted(aggregate_data.keys())
    means_100ms = [np.mean(aggregate_data[m]['100ms']) for m in marker_nums]
    means_50ms = [np.mean(aggregate_data[m]['50ms']) for m in marker_nums]
    means_5ms = [np.mean(aggregate_data[m]['5ms']) for m in marker_nums]
    means_0ms = [np.mean(aggregate_data[m]['0ms']) for m in marker_nums]
    
    # Calculate standard errors
    stderr_100ms = [np.std(aggregate_data[m]['100ms']) / np.sqrt(len(aggregate_data[m]['100ms'])) for m in marker_nums]
    stderr_50ms = [np.std(aggregate_data[m]['50ms']) / np.sqrt(len(aggregate_data[m]['50ms'])) for m in marker_nums]
    stderr_5ms = [np.std(aggregate_data[m]['5ms']) / np.sqrt(len(aggregate_data[m]['5ms'])) for m in marker_nums]
    stderr_0ms = [np.std(aggregate_data[m]['0ms']) / np.sqrt(len(aggregate_data[m]['0ms'])) for m in marker_nums]
    
    # Create the aggregate plot
    plt.figure(figsize=(15, 8))
    x_pos = np.arange(len(marker_nums))
    width = 0.2
    
    # Plot bars with error bars
    plt.bar(x_pos - width*1.5, means_100ms, width, yerr=stderr_100ms, label='NaN gaps 0.10s-0.4s', 
            color='blue', alpha=0.6, capsize=5)
    plt.bar(x_pos - width*0.5, means_50ms, width, yerr=stderr_50ms, label='NaN gaps 0.05s-0.4s', 
            color='red', alpha=0.6, capsize=5)
    plt.bar(x_pos + width*0.5, means_5ms, width, yerr=stderr_5ms, label='NaN gaps 0.005s-0.4s', 
            color='green', alpha=0.6, capsize=5)
    plt.bar(x_pos + width*1.5, means_0ms, width, yerr=stderr_0ms, label='NaN gaps 0.0s-0.4s', 
            color='purple', alpha=0.6, capsize=5)
    
    plt.xlabel('Marker')
    plt.ylabel('Average Blinks per Minute')
    plt.title('Average Blink Frequencies by Marker\nAcross All Subjects')
    plt.xticks(x_pos, marker_nums, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save aggregate plot
    output_dir = "results/blink_plots"
    os.makedirs(output_dir, exist_ok=True)
    plt.savefig(os.path.join(output_dir, "aggregate_blinks.png"))
    plt.close()

def main():
    data_dir = "database/raw_eyetracking"
    aggregate_data = {}

    # Process all files and collect aggregate data
    for filename in os.listdir(data_dir):
        if filename.endswith(".csv"):
            file_path = os.path.join(data_dir, filename)
            print(f"Processing {filename}...")
            plot_blink_frequencies(file_path, aggregate_data)
            # print(f"Plot saved for {filename}")
    
    # Create and save aggregate plot
    print("Creating aggregate plot...")
    plot_aggregate_blinks(aggregate_data)
    print("Aggregate plot saved")

if __name__ == "__main__":
    main()
