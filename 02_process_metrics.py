import pandas as pd
import numpy as np
import os
import re
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from scipy.stats import kurtosis, skew
from scipy import ndimage
from tools.proc import (
    fixation_detection_velocity,
    calculate_velocity,
    saccade_detection_velocity_with_direction,
    get_fixations_for_markers,
    get_pupil_diameter_per_marker,
    get_fixation_frequency_per_marker,
    get_saccade_frequency_per_marker,
    get_saccade_velocity_per_saccade_per_marker,
    get_peak_velocity_per_saccade_per_marker,
    fixation_length_per_marker,
    get_hull_size,
    get_blinks_per_marker,
    get_marker_duration,
    load_bounding_boxes,
    load_image_for_marker,
    get_scanpath_lengths_per_marker,
    )

class MetricsProcessor:
    def __init__(self, data_dir="database/raw_eyetracking"):
        self.data_dir = data_dir
        self.results_df = None
        self.total_positions = {}
        self.gather_total_positions = False
        self.subjs_with_missing_markers_dict = pd.read_csv("database/missing_markers.csv").groupby('subject')['missing_marker'].count().to_dict()

    def extract_metadata(self, filename):
        """Extract metadata from filename using regex."""
        pattern = r'subj-(\d+)_group-(\d+)_role-(\w+)_eye_tracking'
        match = re.match(pattern, filename)
        if match:
            return {
                'subject_id': match.group(1),
                'group': match.group(2),
                'role': match.group(3)
            }
        return None

    def process_single_file(self, filepath):
        """Process a single file and return metrics as a dictionary."""
        try:
            df = pd.read_csv(filepath, sep=";")
        except pd.errors.EmptyDataError:
            print("File is empty")
            return {}
        # check if the subject is in the list of subjects with missing markers
        subject_id = os.path.basename(filepath).split('_')[0]
        if subject_id in self.subjs_with_missing_markers_dict:
            print(f'skipping subject with {self.subjs_with_missing_markers_dict[subject_id]} missing markers: ', subject_id)
            return {}
        # Extract basic data
        x = df["Screen_X_filtered"].to_numpy()
        y = df["Screen_Y_filtered"].to_numpy()
        t = df["Timestamp"].to_numpy()
        markers = df["Marker"].to_numpy()
        pupil_left = df["Diameter_left"].to_numpy() * 2
        pupil_right = df["Diameter_right"].to_numpy() * 2
        # get_blink_frequency_rolling_window(x, y, t, markers=markers, window_size=250*5, min_blink_duration=0.1, max_blink_duration=0.4)
        # # Generate velocity plot before fixation detection
        # filename = os.path.basename(filepath)
        # plot_path = os.path.join(self.plots_dir, f"velocities_{filename.replace('.csv', '.png')}")

        # Calculate velocities and detect fixations/saccades
        velocities = calculate_velocity(x, y, t)
        v_threshold=500

        # plot_velocities(velocities, t, v_threshold=v_threshold, output_path=plot_path)
        print('get fixations by velocity...')
        fixations = fixation_detection_velocity(velocities, t, v_threshold=v_threshold)
        # saccades = saccade_detection_velocity(velocities, v_threshold=v_threshold+50)  # Higher threshold for saccades
        saccades = saccade_detection_velocity_with_direction(x, y, velocities, v_threshold=v_threshold+50, angle_threshold=45, filter_near_nan=True, nan_buffer=10)
        
        # print('get fixations by dispersion...')
        # fixations_disp = fixation_detection_dispersion(x, y, t, d_threshold=200, duration_threshold=0.1)
        # Calculate all metrics per marker
        metrics = {}
        fixations_vel_per_marker = get_fixations_for_markers(markers, fixations)
        # fixations_disp_per_marker = get_fixations_for_markers(markers, fixations_disp)

        pupil_diameter_per_marker = get_pupil_diameter_per_marker(markers, pupil_left, pupil_right)
        fix_freq_per_marker = get_fixation_frequency_per_marker(markers, fixations, t)

        saccade_velocities = get_saccade_velocity_per_saccade_per_marker(velocities, markers, saccades)
        peak_saccade_velocities = get_peak_velocity_per_saccade_per_marker(velocities, markers, saccades)
        sac_freq_per_marker = get_saccade_frequency_per_marker(markers, saccades, t)
        # peak_vel_per_marker = get_peak_velocity_per_marker(velocities, markers)
        # backtrack_per_marker = backtracking_to_previous_paragraphs(x, y, markers, fixations)
        fix_length_per_marker = fixation_length_per_marker(markers, fixations, t)
        blink_freq_per_marker = get_blinks_per_marker(markers, x, y, t, max_blink_duration=0.4, min_blink_duration=0.07)
        marker_length = get_marker_duration(markers, t)
        scanpath_length = get_scanpath_lengths_per_marker(markers, fixations, x, y)
        print('looping over markers...')
        if pd.unique(markers[markers >= 100]).shape[0] == 0:
            print('no markers found')

            return metrics
        for marker in pd.unique(markers[markers >= 100]):
            
            # if int(self.get_marker_quality(int(str(marker)[0]), subject_id)) == 0:
            #     print('skipping marker: ', marker)
            #     continue
            marker_fixations_vel = fixations_vel_per_marker[marker]
            print('\tnumber of fixations: ', marker_fixations_vel.shape[0])
            print('\tnumber of saccades: ', saccade_velocities[marker].shape[0])
            print('getting metrics for marker: ', marker)
            marker_fixations = marker_fixations_vel
            pupil_diameter_avg = np.nanmean(pupil_diameter_per_marker[marker])
            pupil_diameter_var = np.nanvar(pupil_diameter_per_marker[marker], mean=pupil_diameter_avg)
            pupil_diameter_kurtosis = kurtosis(pupil_diameter_per_marker[marker], nan_policy='omit')
            pupil_diameter_skewness = skew(pupil_diameter_per_marker[marker], nan_policy='omit')

            fixation_duration_avg = np.nanmean(fix_length_per_marker[marker])
            fixation_duration_var = np.nanvar(fix_length_per_marker[marker], mean=fixation_duration_avg)
            fixation_duration_kurtosis = kurtosis(fix_length_per_marker[marker], nan_policy='omit')
            fixation_duration_skewness = skew(fix_length_per_marker[marker], nan_policy='omit')

            peak_sac_vel_avg = np.nanmean(peak_saccade_velocities[marker])
            peak_sac_vel_var = np.nanvar(peak_saccade_velocities[marker], mean=peak_sac_vel_avg)
            peak_sac_vel_kurtosis = kurtosis(peak_saccade_velocities[marker], nan_policy='omit')
            peak_sac_vel_skewness = skew(peak_saccade_velocities[marker], nan_policy='omit')

            sac_vel_avg = np.nanmean(saccade_velocities[marker])
            sac_vel_var = np.nanvar(saccade_velocities[marker], mean=sac_vel_avg)
            sac_vel_kurtosis = kurtosis(saccade_velocities[marker], nan_policy='omit')
            sac_vel_skewness = skew(saccade_velocities[marker], nan_policy='omit')


            metrics[marker] = {
                'pupil_diameter_avg': pupil_diameter_avg,
                'pupil_diameter_var': pupil_diameter_var,
                'pupil_diameter_kurtosis': pupil_diameter_kurtosis,
                'pupil_diameter_skewness': pupil_diameter_skewness,

                'fixation_length_avg': fixation_duration_avg,
                'fixation_length_var': fixation_duration_var,
                'fixation_length_kurtosis': fixation_duration_kurtosis,
                'fixation_length_skewness': fixation_duration_skewness,

                'saccade_velocity_avg': sac_vel_avg,
                'saccade_velocity_var': sac_vel_var,
                'saccade_velocity_kurtosis': sac_vel_kurtosis,
                'saccade_velocity_skewness': sac_vel_skewness,

                'peak_saccade_velocity_avg': peak_sac_vel_avg,
                'peak_saccade_velocity_var': peak_sac_vel_var,
                'peak_saccade_velocity_kurtosis': peak_sac_vel_kurtosis,
                'peak_saccade_velocity_skewness': peak_sac_vel_skewness,

                'fixation_frequency_vel': fix_freq_per_marker[marker],
                'saccade_frequency_vel': sac_freq_per_marker[marker],
                # 'backtracking': backtrack_per_marker[marker],

                'blink_freq': blink_freq_per_marker[marker],
                'marker_length': marker_length[marker],
                'scanpath_length': np.nansum(scanpath_length[marker]),
                'scanpath_length_avg': np.nanmean(scanpath_length[marker]),
              }
            if self.gather_total_positions:
                if marker not in self.total_positions:
                    self.total_positions[marker] = {'x':np.array([]),
                                                    'y':np.array([])}
                self.total_positions[marker]['x'] = np.concatenate((self.total_positions[marker]['x'],x[markers==marker]))
                self.total_positions[marker]['y'] = np.concatenate((self.total_positions[marker]['y'],y[markers==marker]))
      
            # Calculate hull sizes for fixations in this marker
            if len(marker_fixations) > 0:
                hull_sizes = get_hull_size(x, y, marker_fixations)
                metrics[marker]['mean_hull_size'] = np.nanmean(hull_sizes)
                metrics[marker]['max_hull_size'] = np.nanmax(hull_sizes)
            else:
                metrics[marker]['mean_hull_size'] = 0
                metrics[marker]['max_hull_size'] = 0

            # # add metrics normalised by marker length
            metrics[marker]['fixation_length_vel_norm'] = metrics[marker]['fixation_length_avg'] / metrics[marker]['marker_length']
            metrics[marker]['scanpath_length_norm'] = metrics[marker]['scanpath_length'] / metrics[marker]['marker_length']
            metrics[marker]['mean_hull_size_norm'] = metrics[marker]['mean_hull_size'] / metrics[marker]['marker_length']
            metrics[marker]['max_hull_size_norm'] = metrics[marker]['max_hull_size'] / metrics[marker]['marker_length']

        return metrics

    def process_all_files(self):
        """Process all files and create a comprehensive DataFrame."""
        all_data = []

        for filename in os.listdir(self.data_dir):
            if not filename.endswith('.csv'):
                continue

            filepath = os.path.join(self.data_dir, filename)
            metadata = self.extract_metadata(filename)
            
            if not metadata:
                continue

            print(f"Processing {filename}...")
            metrics = self.process_single_file(filepath)

            # Flatten metrics into rows
            for marker, marker_metrics in metrics.items():
                row_data = {
                    'subject_id': metadata['subject_id'],
                    'group': metadata['group'],
                    'role': metadata['role'],
                    'marker': marker,
                    'scenario': str(marker)[0],
                    'ti_type': str(marker)[1],
                    'page': str(marker)[2],
                    **marker_metrics
                }
                all_data.append(row_data)



        # Create DataFrame and save
        self.results_df = pd.DataFrame(all_data)
        return self.results_df

    def save_results(self, output_dir="results"):
        """Save results to CSV."""
        if self.results_df is None:
            raise ValueError("No results to save. Run process_all_files first.")

        os.makedirs(output_dir, exist_ok=True)
        
        # Save complete dataset
        self.results_df.to_csv(f"{output_dir}/results_dataframe_format.csv", index=False)

    def plot_heatmaps(self, examplefile=None):
        colors = [(1,0,0,c) for c in np.linspace(0,1,100)]
        cmapred = mcolors.LinearSegmentedColormap.from_list('mycmap', colors, N=10) 
        os.makedirs("results/heatmaps", exist_ok=True)
        
        # Get all markers and find a subject with all markers
        bounding_box_json = load_bounding_boxes()
        all_markers = [img['id'] for img in bounding_box_json['images']]
        
        if examplefile is None:
            # Find a complete subject data file
            example_subject_file = None
            for filename in os.listdir(self.data_dir):
                if not filename.endswith('.csv'):
                    continue
                
                df = pd.read_csv(os.path.join(self.data_dir, filename), sep=";")
                subject_markers = df['Marker'].unique()
                if all(marker in subject_markers for marker in all_markers):
                    example_subject_file = os.path.join(self.data_dir, filename)
                    print(f"Using {example_subject_file} for example gaze path")
                    break
            
            if example_subject_file is None:
                print("No subject with all markers found. Plotting heatmaps without gaze path.")
                return
        else:
            example_subject_file =os.path.join(self.data_dir, examplefile)

        # Load example subject data
        example_df = pd.read_csv(example_subject_file, sep=";")
        print(all_markers)
        for m in all_markers:
            
            print(f"Plotting {m}...")
            # positions = self.total_positions[m]
            
            # # Create heatmap
            # heatmap = np.zeros((1200, 1920))
            # for x, y in zip(positions['x'], positions['y']):
            #     if np.isnan(x) or np.isnan(y):
            #         continue
            #     if x < 0 or x > 1920 or y < 0 or y > 1200:
            #         continue
            #     heatmap[int(y), int(x)] += 1
            
            # heatmap = ndimage.gaussian_filter(heatmap, sigma=10)
            # heatmap = (heatmap - np.min(heatmap)) / (np.max(heatmap) - np.min(heatmap))
            
            # Get example subject data for this marker
            marker_data = example_df[example_df['Marker'] == m]
            x = marker_data["Screen_X_filtered"].to_numpy()
            y = marker_data["Screen_Y_filtered"].to_numpy()
            t = marker_data["Timestamp"].to_numpy()
            
            # Calculate fixations for example subject
            fixations = fixation_detection_velocity(x, y, t, v_threshold=500)
            
            # Create plot
            plt.figure(figsize=(19.2, 12), dpi=100)
            plt.subplots_adjust(left=0, right=1, top=1, bottom=0)
            plt.axis('off')
            
            # Plot background and heatmap
            image = load_image_for_marker(m)
            plt.imshow(image)
            # plt.imshow(heatmap, alpha=0.5, cmap=cmapred)
            
            # Plot gaze path
            plt.plot(x, y, 'black', alpha=0.3, linewidth=0.5, label='Gaze path')
            
            # Plot fixations
            for start, end in fixations:
                fix_x = np.mean(x[start:end])
                fix_y = np.mean(y[start:end])
                plt.plot(fix_x, fix_y, 'o', color='blue', alpha=0.7, markersize=8)
            os.makedirs(f"results/{example_subject_file.split('\\')[-1].split('_')[0]}", exist_ok=True)
            # Save the plot
            plt.savefig(f"results/{example_subject_file.split("\\")[-1].split('_')[0]}/{m}.png", 
                       bbox_inches='tight', 
                       pad_inches=0, 
                       dpi=100)
            plt.close()


        
def main():
    processor = MetricsProcessor()
    results = processor.process_all_files()
    processor.save_results()
    #plot all heatmaps for every file:
    # for file in os.listdir(processor.data_dir):
    #     processor.plot_heatmaps(file)
    print("Processing complete. Results saved in the 'results' directory.")
    print("\nSample of the results:")
    print(results.head())

if __name__ == "__main__":
    main()
