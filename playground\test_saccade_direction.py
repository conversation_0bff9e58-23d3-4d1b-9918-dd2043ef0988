import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from tools.proc import (
    calculate_velocity,
    calculate_acceleration,
    saccade_detection_velocity,
    saccade_detection_velocity_with_direction,
    saccade_detection_acceleration
)

def compare_saccade_detection_methods(filepath, marker=113):
    """Compare different saccade detection methods."""
    # Read data
    df = pd.read_csv(filepath, sep=";")
    df = df[df['Marker'] == marker]
    
    x = df["Screen_X_filtered"].to_numpy()
    y = df["Screen_Y_filtered"].to_numpy()
    t = df["Timestamp"].to_numpy()
    
    # Calculate velocities and accelerations
    velocities = calculate_velocity(x, y, t)

    # Detect saccades using different methods
    v_threshold = 500
    velocity_saccades = saccade_detection_velocity(velocities, v_threshold=v_threshold)
    direction_saccades = saccade_detection_velocity_with_direction(x, y, velocities, v_threshold=v_threshold)
    acceleration_saccades = saccade_detection_acceleration(x, y, t, velocities, 
                                                          v_threshold=v_threshold,
                                                          acc_threshold=2000, 
                                                          deacc_threshold=-2000)
    
    # Plot results
    plt.figure(figsize=(15, 15))
    
    # Plot 1: Velocity-based saccade detection
    plt.subplot(3, 1, 1)
    plt.plot(x, y, 'gray', alpha=0.5, linewidth=0.5)
    
    for i, (start, end) in enumerate(velocity_saccades):
        plt.plot(x[start:end+1], y[start:end+1], 'b-', linewidth=1.5)
        mid_x = np.mean(x[start:end+1])
        mid_y = np.mean(y[start:end+1])
        plt.text(mid_x, mid_y, str(i), color='blue', fontsize=8)
    
    plt.title('Standard Velocity-based Saccade Detection')
    plt.xlabel('X position')
    plt.ylabel('Y position')
    plt.grid(alpha=0.3)
    
    # Plot 2: Direction-aware saccade detection
    plt.subplot(3, 1, 2)
    plt.plot(x, y, 'gray', alpha=0.5, linewidth=0.5)
    
    for i, (start, end) in enumerate(direction_saccades):
        plt.plot(x[start:end+1], y[start:end+1], 'r-', linewidth=1.5)
        mid_x = np.mean(x[start:end+1])
        mid_y = np.mean(y[start:end+1])
        plt.text(mid_x, mid_y, str(i), color='red', fontsize=8)
    
    plt.title('Direction-aware Saccade Detection')
    plt.xlabel('X position')
    plt.ylabel('Y position')
    plt.grid(alpha=0.3)
    
    # Plot 3: Acceleration-based saccade detection
    plt.subplot(3, 1, 3)
    plt.plot(x, y, 'gray', alpha=0.5, linewidth=0.5)
    
    for i, (start, end) in enumerate(acceleration_saccades):
        plt.plot(x[start:end+1], y[start:end+1], 'g-', linewidth=1.5)
        mid_x = np.mean(x[start:end+1])
        mid_y = np.mean(y[start:end+1])
        plt.text(mid_x, mid_y, str(i), color='green', fontsize=8)
    
    plt.title('Acceleration-based Saccade Detection')
    plt.xlabel('X position')
    plt.ylabel('Y position')
    plt.grid(alpha=0.3)
    
    # Print statistics
    print(f"Velocity-based saccades detected: {len(velocity_saccades)}")
    print(f"Direction-aware saccades detected: {len(direction_saccades)}")
    print(f"Acceleration-based saccades detected: {len(acceleration_saccades)}")
    
    plt.tight_layout()
    plt.show()
    
    return velocity_saccades, direction_saccades, acceleration_saccades

if __name__ == "__main__":
    filepath = "database/raw_eyetracking/subj-01_group-1_role-Operator_eye_tracking_20241210094555.csv"
    
    # Compare different saccade detection methods
    velocity_saccades, direction_saccades, acceleration_saccades = compare_saccade_detection_methods(filepath, marker=413)
    
    # Original functions can still be called if needed
    # saccade_directions = analyze_saccade_directions(filepath, marker=413)
    # standard_saccades, modified_saccades = test_modified_saccade_detection(filepath, marker=413)

