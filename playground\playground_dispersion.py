import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import time
import numpy as np
import pandas as pd
import cv2
from tools.proc import fixation_detection_dispersion


def load_image_for_marker(marker):
    """
    Lädt das passende Bild für einen gegebenen Markerwert basierend auf den Codes.
    """
    scenario = {"1": 'a', "2": 'b', "3": 'c', "4": 'd', "5": 'e', "6": 'f'}
    ti_type = {"1": "system_goal", "2": "system_type", "3": "data", "4": "control"}

    marker_str = str(marker).zfill(3)  # Si<PERSON><PERSON><PERSON>, dass der Marker dreistellig ist
    scenario_key, ti_key, page = marker_str[0], marker_str[1], marker_str[2]

    if scenario_key in scenario and ti_key in ti_type:
        image_filename = f"database/ti_png/{scenario[scenario_key]}_{ti_type[ti_key]}_{page}.png"
        if os.path.exists(image_filename):
            return cv2.imread(image_filename)
    return cv2.imread(f"database/ti_png/default.png")

d_path = "database/raw_eyetracking/subj-08_group-2_role-User_eye_tracking_20241212094106.csv"
df = pd.read_csv(d_path, sep=";")

x = df["Screen_X_filtered"].to_numpy()
y = df["Screen_Y_filtered"].to_numpy()
timestamp = df["Timestamp"].to_numpy()
markers = df["Marker"].to_numpy()

print("Dataframe loaded")
fixations_idx = fixation_detection_dispersion(x, y, timestamp, d_threshold=20, duration_threshold=0.1)
fixations = np.zeros_like(x, dtype=np.uint8)
print("Dispersion Check finished")
for start, end in fixations_idx:
    fixations[start:end] = 1

height, width = 1200, 1920
canvas = np.zeros((height, width), dtype=np.uint8)
# Konvertiere das Canvas in ein 3-Kanal-Bild
canvas_bgr = cv2.cvtColor(canvas, cv2.COLOR_GRAY2BGR)
last_fix_coord_x, last_fix_coord_y = 0, 0
last_fix = -1

for frame, (fx, fy) in enumerate(zip(x, y)):
    
    if markers[frame]  < 100:
        continue
    canvas.fill(0)
    print(markers[frame])
    image_overlay = load_image_for_marker(markers[frame])
    if image_overlay is not None:
        image_overlay = cv2.resize(image_overlay, (width, height))
        canvas = cv2.addWeighted(canvas_bgr, 0.0, image_overlay, 1.0, 0)

    if np.isnan(fy) or np.isnan(fx):
        continue

    cv2.circle(canvas, (int(last_fix_coord_x), int(last_fix_coord_y)), radius=20, color=50, thickness=-1)

    if fixations[frame]:
        cv2.circle(canvas, (int(fx), int(fy)), radius=30, color=0, thickness=5)
        cv2.circle(canvas, (int(fx), int(fy)), radius=20, color=(0,0,255), thickness=-1)
    else:
        cv2.circle(canvas, (int(fx), int(fy)), radius=30, color=0, thickness=5)
        if last_fix == 1:
            last_fix_coord_x, last_fix_coord_y = fx, fy


    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(canvas, str(timestamp[frame]), (50, 50), font, 1, (0, 0, 0), 2, cv2.LINE_AA)
   
    last_fix = fixations[frame]

    cv2.imshow("Real-Time Gaze Point", canvas)
    if cv2.waitKey(4) & 0xFF == ord('q'):
        break