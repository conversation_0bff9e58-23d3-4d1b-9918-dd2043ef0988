import json
import cv2
import numpy as np
import os
# import pytesseract
# from PIL import Image

def load_bounding_boxes():
    """Load paragraph bounding boxes from JSON file."""
    with open("database/bounding_boxes/Bounding_Boxes.json", "r") as f:
        return json.load(f)

def load_image_for_marker(marker):
    """Load the corresponding image for a given marker."""
    scenario = {"1": 'a', "2": 'b', "3": 'c', "4": 'd', "5": 'e', "6": 'f'}
    ti_type = {"1": "system_goal", "2": "system_type", "3": "data", "4": "control"}

    marker_str = str(marker).zfill(3)
    scenario_key, ti_key, page = marker_str[0], marker_str[1], marker_str[2]

    if scenario_key in scenario and ti_key in ti_type:
        image_filename = f"database/ti_png/{scenario[scenario_key]}_{ti_type[ti_key]}_{page}.png"
        if os.path.exists(image_filename):
            return cv2.imread(image_filename)
    return None

def detect_words_in_paragraph(img, paragraph_box):
    """
    Detect words within a paragraph using OCR and image processing.
    
    Args:
        img: Full image
        paragraph_box: Dictionary with top_left and bottom_right coordinates of paragraph
        
    Returns:
        List of word bounding boxes
    """
    # Extract paragraph region
    x1, y1 = paragraph_box["top_left"]["x"], paragraph_box["top_left"]["y"]
    x2, y2 = paragraph_box["bottom_right"]["x"], paragraph_box["bottom_right"]["y"]
    paragraph_img = img[y1:y2, x1:x2]
    
    # Convert to grayscale
    gray = cv2.cvtColor(paragraph_img, cv2.COLOR_BGR2GRAY)
    
    # Apply adaptive thresholding to handle different lighting conditions
    binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                  cv2.THRESH_BINARY_INV, 11, 2)
    cv2.imshow('test', binary)
    # # Perform morphological operations to separate words
    kernel = np.ones((10, 7), np.uint8)  # Horizontal kernel to connect characters
    connected = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    
    # # Use tesseract to get word bounding boxes
    # # Convert OpenCV image to PIL format for tesseract
    # pil_img = Image.fromarray(gray)
    
    # # Get word bounding boxes from tesseract
    # word_data = pytesseract.image_to_data(pil_img, config='--psm 11', output_type=pytesseract.Output.DICT)
    
    word_boxes = []
    # for i in range(len(word_data['text'])):
    #     # Skip empty text
    #     if word_data['text'][i].strip() == '':
    #         continue
        
    #     # Get word confidence
    #     conf = int(word_data['conf'][i])
    #     if conf < 30:  # Skip low confidence detections
    #         continue
        
    #     # Get word bounding box
    #     x = word_data['left'][i]
    #     y = word_data['top'][i]
    #     w = word_data['width'][i]
    #     h = word_data['height'][i]
        
    #     # Adjust coordinates to full image
    #     word_box = {
    #         "top_left": {"x": x1 + x, "y": y1 + y},
    #         "bottom_right": {"x": x1 + x + w, "y": y1 + y + h}
    #     }
    #     word_boxes.append(word_box)
    
    # If tesseract fails to detect words, use contour-based approach as fallback
    if True: # len(word_boxes) == 0:
        # Find contours of connected components
        contours, _ = cv2.findContours(connected, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter contours by size to get words
        min_width = 10  # Minimum width for a word
        min_height = 5  # Minimum height for a word
        
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            if w > min_width and h > min_height:
                # Adjust coordinates to full image
                word_box = {
                    "top_left": {"x": x1 + x, "y": y1 + y},
                    "bottom_right": {"x": x1 + x + w, "y": y1 + y + h}
                }
                word_boxes.append(word_box)
    
    return word_boxes

def main():
    # Load paragraph bounding boxes
    paragraph_boxes = load_bounding_boxes()
    marker_images = paragraph_boxes['images']
    
    # Create new structure for word bounding boxes
    word_bounding_boxes = {"images": []}
    
    # Process each marker
    for marker_data in marker_images:
        marker_id = marker_data['id']
        print(f"Processing marker {marker_id}")
        
        # Load image
        img = load_image_for_marker(marker_id)
        if img is None:
            print(f"No image found for marker {marker_id}")
            continue
        
        # Detect words in each paragraph
        all_word_boxes = []
        for i, paragraph_box in enumerate(marker_data['bounding_boxes']):
            word_boxes = detect_words_in_paragraph(img, paragraph_box)
            all_word_boxes.extend(word_boxes)
            print(f"  Detected {len(word_boxes)} words in paragraph {i}")
        
        # Save word boxes for this marker
        word_bounding_boxes["images"].append({
            "id": marker_id,
            "word_boxes": all_word_boxes
        })
        
        # Visualize the detected words
        img_copy = img.copy()
        
        # Draw paragraph boxes
        for i, box in enumerate(marker_data['bounding_boxes']):
            cv2.rectangle(img_copy, 
                         (box["top_left"]["x"], box["top_left"]["y"]),
                         (box["bottom_right"]["x"], box["bottom_right"]["y"]),
                         (255, 0, 0), 2)  # Paragraphs in blue
            # Add label
            cv2.putText(img_copy, f"P{i}", 
                       (box["top_left"]["x"], box["top_left"]["y"] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 0, 0), 2)
        
        # Draw word boxes
        for box in all_word_boxes:
            cv2.rectangle(img_copy, 
                         (box["top_left"]["x"], box["top_left"]["y"]),
                         (box["bottom_right"]["x"], box["bottom_right"]["y"]),
                         (0, 255, 0), 1)  # Words in green
        
        # Display the image with detected words
        cv2.imshow(f"Marker {marker_id} - Detected Words", img_copy)
        key = cv2.waitKey(0)  # Wait for key press
        if key == ord('q'):  # Quit if 'q' is pressed
            cv2.destroyAllWindows()
            break
        cv2.destroyWindow(f"Marker {marker_id} - Detected Words")
    
    # Save word bounding boxes to file
    with open("database/bounding_boxes/Word_Bounding_Boxes.json", "w") as f:
        json.dump(word_bounding_boxes, f, indent=2)
    
    print("Word bounding boxes saved to database/bounding_boxes/Word_Bounding_Boxes.json")

if __name__ == "__main__":
    main()