import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import os
import numpy as np
def plot_metrics():
    '''plot stripplots containing all data of a metric, with subjects as hue'''
    # Load processed data
    input_file = "results/results_dataframe_format.csv"
    output_dir = "results/plots"
    os.makedirs(output_dir, exist_ok=True)
    
    # Load the results dataframe
    df = pd.read_csv(input_file)
    df = df[df['subject_id'] != 4]
    df = df[df['subject_id'] != 21]
    # Get unique metrics and subjects
    # Exclude non-metric columns
    exclude_cols = ['subject_id', 'group', 'role', 'marker', 'scenario', 'ti_type', 'page']
    metrics = [col for col in df.columns if col not in exclude_cols]
    subjects = df['subject_id'].unique()

    #clean nans or infs:
    df = df.replace([np.inf, -np.inf,], np.nan)
    df = df.replace(0, 0.001)
    df = df.dropna()
    # Plot each metric
    for metric in metrics:
        plt.figure(figsize=(30, 8))
        sns.violinplot(data=df, x='subject_id', y=metric, dodge=True)
        plt.title(f'Distribution of {metric} per Subject')
        plt.xlabel('Subject ID')
        plt.ylabel(metric)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'unscaled_{metric}_violinplot.png'))
        plt.close()
        print(f"Plot saved for {metric}")

if __name__ == "__main__":
    plot_metrics()
