import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import cv2
from tools.drift_algorithms import attach, chain, cluster, compare, merge, regress, segment, split, stretch, warp
from tools.proc import fixation_detection_velocity, load_words, load_lines, calculate_velocity

def load_image_for_marker(marker):
    """Load the corresponding image for a given marker."""
    scenario = {"1": 'a', "2": 'b', "3": 'c', "4": 'd', "5": 'e', "6": 'f'}
    ti_type = {"1": "system_goal", "2": "system_type", "3": "data", "4": "control"}

    marker_str = str(marker).zfill(3)
    scenario_key, ti_key, page = marker_str[0], marker_str[1], marker_str[2]

    if scenario_key in scenario and ti_key in ti_type:
        image_filename = f"database/ti_png/{scenario[scenario_key]}_{ti_type[ti_key]}_{page}.png"
        return cv2.imread(image_filename)
    return None

def get_line_positions(marker):
    """Get line positions from bounding boxes for a given marker."""
    bounding_boxes = load_lines()
    marker_boxes = [img['line_boxes'] for img in bounding_boxes['images'] if img['id'] == marker]
    
    if not marker_boxes:
        return None
        
    boxes = marker_boxes[0]
    # Extract y-coordinates of the center of each box
    line_Y = []
    for box in boxes:
        y1 = box['top_left']['y']
        y2 = box['bottom_right']['y']
        line_Y.append((y1 + y2) / 2)
    
    return np.array(sorted(line_Y))

def get_word_positions(marker):
    """Get word positions from bounding boxes for a given marker."""
    bounding_boxes = load_words()
    marker_boxes = [img['word_boxes'] for img in bounding_boxes['images'] if img['id'] == marker]

    if not marker_boxes:
        return None
        
    boxes = marker_boxes[0]
    # Extract xy-coordinates of the center of each word
    word_XY = []
    for box in boxes:
        y1 = box['top_left']['y']
        y2 = box['bottom_right']['y']
        x1 = box['top_left']['x']
        x2 = box['bottom_right']['x']
        word_XY.append([(x1 + x2) / 2, (y1 + y2) / 2])
    
    return np.array(word_XY)

def filter_fixations_outside_paragraphs(fixation_XY, marker, margin=50):
    """Filter out fixations outside paragraph boundaries."""
    bounding_boxes = load_lines()
    marker_boxes = [img['line_boxes'] for img in bounding_boxes['images'] if img['id'] == marker]
    
    if not marker_boxes:
        return fixation_XY
    
    paragraph_boxes = marker_boxes[0]
    
    # Find the top and bottom y-coordinates of the paragraph
    top_y = min(box['top_left']['y'] for box in paragraph_boxes)
    bottom_y = max(box['bottom_right']['y'] for box in paragraph_boxes)
    
    # Add margin to the boundaries
    top_y -= margin
    bottom_y += margin
    
    # Filter fixations within the paragraph boundaries
    filtered_fixation_XY = fixation_XY[(fixation_XY[:, 1] >= top_y) & (fixation_XY[:, 1] <= bottom_y)]
    
    return filtered_fixation_XY

def apply_drift_correction(filepath, marker, algorithm_name):
    """Apply a specific drift correction algorithm to fixations."""
    # Read data
    df = pd.read_csv(filepath, sep=";")
    df = df[df['Marker'] == marker]
    
    if len(df) == 0:
        return None, None, None
    
    x = df["Screen_X_filtered"].to_numpy()
    y = df["Screen_Y_filtered"].to_numpy()
    t = df["Timestamp"].to_numpy()
    
    # Calculate velocities
    velocities = calculate_velocity(x, y, t)

    # Get fixations
    fixations_indices = fixation_detection_velocity(velocities, t, v_threshold=2000)
    
    # Extract fixation coordinates
    fixation_XY = np.zeros((len(fixations_indices), 2))
    for i, (start, end) in enumerate(fixations_indices):
        fixation_XY[i, 0] = np.mean(x[start:end])
        fixation_XY[i, 1] = np.mean(y[start:end])
    
    # Filter out fixations at the bottom and top of the screen
    # Adjust these thresholds based on your screen resolution
    bottom_threshold = 900  # Exclude fixations below this y-value
    top_threshold = 150     # Exclude fixations above this y-value
    
    # Keep only fixations within the desired region
    mask = (fixation_XY[:, 1] < bottom_threshold) & (fixation_XY[:, 1] > top_threshold)
    fixation_XY = fixation_XY[mask]
    
    # Filter out fixations outside paragraph boundaries
    fixation_XY = filter_fixations_outside_paragraphs(fixation_XY, marker, margin=50)

    if len(fixation_XY) == 0:
        return None, None, None
    
    # Get line positions
    line_Y = get_line_positions(marker)
    
    if line_Y is None or len(line_Y) < 2:
        return None, None, None
    
    # Create word_XY for algorithms that need it
    # This is a simplified version - in real use, you'd have actual word positions
    word_XY = np.zeros((len(line_Y) * 10, 2))
    for i, y in enumerate(line_Y):
        word_XY[i*10:(i+1)*10, 1] = y
        word_XY[i*10:(i+1)*10, 0] = np.linspace(100, 1000, 10)  # Spread words across x-axis
    
    # Apply the specified algorithm
    original_fixation_XY = fixation_XY.copy()
    
    if algorithm_name == "attach":
        corrected_fixation_XY = attach(fixation_XY.copy(), line_Y)
    elif algorithm_name == "chain":
        corrected_fixation_XY = chain(fixation_XY.copy(), line_Y)
    elif algorithm_name == "cluster":
        corrected_fixation_XY = cluster(fixation_XY.copy(), line_Y)
    elif algorithm_name == "compare":
        corrected_fixation_XY = compare(fixation_XY.copy(), word_XY)
    elif algorithm_name == "merge":
        corrected_fixation_XY = merge(fixation_XY.copy(), line_Y)
    elif algorithm_name == "regress":
        corrected_fixation_XY = regress(fixation_XY.copy(), line_Y)
    elif algorithm_name == "segment":
        corrected_fixation_XY = segment(fixation_XY.copy(), line_Y)
    elif algorithm_name == "split":
        corrected_fixation_XY = split(fixation_XY.copy(), line_Y)
    elif algorithm_name == "stretch":
        corrected_fixation_XY = stretch(fixation_XY.copy(), line_Y)
    elif algorithm_name == "warp":
        corrected_fixation_XY = warp(fixation_XY.copy(), word_XY)
    else:
        return None, None, None
    
    return original_fixation_XY, corrected_fixation_XY, line_Y

def plot_drift_correction_comparison(filepath, markers, algorithms):
    """Plot comparison of different drift correction algorithms on different markers."""
    n_markers = len(markers)
    n_algorithms = len(algorithms)
    
    fig, axes = plt.subplots(n_markers, n_algorithms, figsize=(n_algorithms*5, n_markers*5))
    
    for i, marker in enumerate(markers):
        image = load_image_for_marker(marker)
        if image is None:
            print(f"No image found for marker {marker}")
            continue
        
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        for j, algo in enumerate(algorithms):
            ax = axes[i, j] if n_markers > 1 else axes[j]
            
            # Apply algorithm
            original, corrected, line_Y = apply_drift_correction(filepath, marker, algo)
            
            if original is None:
                ax.text(0.5, 0.5, f"No data for marker {marker}", ha='center', va='center')
                ax.set_title(f"Marker {marker} - {algo}")
                continue
            
            # Plot image
            ax.imshow(image)
            
            # Plot original fixations
            ax.scatter(original[:, 0], original[:, 1], color='red', alpha=0.5, label='Original')
            
            # Plot corrected fixations
            ax.scatter(corrected[:, 0], corrected[:, 1], color='blue', alpha=0.5, label='Corrected')
            
            # Plot line positions
            for y in line_Y:
                ax.axhline(y=y, color='green', linestyle='--', alpha=0.5)
            
            # Connect original to corrected with lines
            for k in range(len(original)):
                ax.plot([original[k, 0], corrected[k, 0]], 
                        [original[k, 1], corrected[k, 1]], 
                        'k-', alpha=0.2)
            
            ax.set_title(f"Marker {marker} - {algo}")
            ax.legend()
    
    plt.tight_layout()
    plt.savefig("drift_correction_comparison.png", dpi=300)
    plt.show()

if __name__ == "__main__":
    filepath = "database/raw_eyetracking/subj-01_group-1_role-Operator_eye_tracking_20241210094555.csv"
    
    # Select markers to compare
    markers = [113, 121, 132]
    
    # Select algorithms to compare
    algorithms = ["attach", "chain", "cluster", "merge", "regress", "split"]
    
    plot_drift_correction_comparison(filepath, markers, algorithms)


