import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import pandas as pd
import matplotlib.pyplot as plt
import os
from tools.proc import get_paragraph_from_coords, fixation_detection_velocity

def plot_markers_and_boxes(filepath):
    # Read the CSV file
    df = pd.read_csv(filepath, sep=";")
    
    # Get necessary data
    markers = df["Marker"].to_numpy()
    timestamps = df["Timestamp"].to_numpy()
    x = df["Screen_X_filtered"].to_numpy()
    y = df["Screen_Y_filtered"].to_numpy()
    
    # Convert timestamps to relative time in seconds
    relative_time = timestamps - timestamps[0]
    
    # Get fixations
    fixations = fixation_detection_velocity(x, y, timestamps)
    
    # # Get paragraph information
    # paragraphs = get_paragraph_from_coords(x, y, markers, fixations)
    
    # Create arrays for box plotting
    box_times = []
    box_values = []
    
    # # Process each fixation's paragraph information
    # for fix_start_idx, (marker, box_idx) in paragraphs.items():
    #     print(fix_start_idx, marker)
    #     time = relative_time[fix_start_idx]
    #     # Multiply positive box indices by 100, use 290 for no box (-1)
    #     box_value = (box_idx+1) * 100 if box_idx >= 0 else 290
    #     box_times.append(time)
    #     box_values.append(box_value)
    
    # Create the plot
    plt.figure(figsize=(15, 8))
    
    # Plot markers
    plt.plot(relative_time, markers, 'b.', markersize=1, label='Markers')
    
    # Plot boxes
    plt.plot(box_times, box_values, 'r.', markersize=2, label='Bounding Boxes')
    
    plt.grid(True, alpha=0.3)
    plt.xlabel('Time (seconds)')
    plt.ylabel('Value')
    plt.title(filepath)

    plt.legend()
    plt.show()

if __name__ == "__main__":
    for filepath in os.listdir("database/raw_eyetracking"):
        filepath = os.path.join("database/raw_eyetracking", filepath)
        plot_markers_and_boxes(filepath)
