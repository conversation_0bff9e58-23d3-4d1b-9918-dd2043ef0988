import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from tools.proc import (
    calculate_velocity,
    fixation_detection_velocity,
    fixation_detection_dispersion
)

def plot_velocity_and_fixations(filepath, marker=121):
    # Read data
    df_old = pd.read_csv(filepath, sep=";")
    # Filter for specific marker
    df = df_old  # [df_old['Marker'] == marker].copy()
    
    # Get necessary data
    x = df["Screen_X_filtered"].to_numpy()
    y = df["Screen_Y_filtered"].to_numpy()
    t = df["Timestamp"].to_numpy()
    markers = df["Marker"].to_numpy()
    
    # Convert to relative time
    t_rel = (t - t[0]) 
    
    # Calculate velocity
    velocities = calculate_velocity(x, y, t)
    
    # Get fixations using both methods
    fixations_vel = fixation_detection_velocity(velocities, t, v_threshold=3000)
    fixations_disp = fixation_detection_dispersion(x, y, t, d_threshold=200)
    
    # Create binary arrays for fixation periods
    vel_periods = np.zeros_like(t_rel)
    disp_periods = np.zeros_like(t_rel)
    
    for start, end in fixations_vel:
        vel_periods[start:end] = 1
    
    for start, end in fixations_disp:
        disp_periods[start:end] = 1
    
    # Plot
    plt.figure(figsize=(15, 8))
    
    # Plot velocity
    plt.plot(t_rel, velocities, 'gray', alpha=0.6, label='Velocity')
    
    # Plot fixation periods at fixed y-values
    plt.fill_between(t_rel, 600, 800, 
                    where=vel_periods==1, 
                    color='red', alpha=0.3, 
                    label='Velocity-based fixations')
    
    plt.fill_between(t_rel, 800, 1000, 
                    where=disp_periods==1, 
                    color='blue', alpha=0.3, 
                    label='Dispersion-based fixations')
    
    # Add marker change lines
    prev_marker = markers[0]
    for i, marker in enumerate(markers):
        if marker != prev_marker and marker >= 100:
            plt.axvline(x=t_rel[i], color='green', linestyle='--', alpha=0.5)
            plt.text(t_rel[i], plt.ylim()[1], f'M{marker}', 
                    rotation=90, verticalalignment='top')
        prev_marker = marker
    
    # Add threshold line
    plt.axhline(y=500, color='r', linestyle='--', label='Velocity threshold (500)')
    
    plt.grid(True, alpha=0.3)
    plt.xlabel('Time (seconds)')
    plt.ylabel('Velocity (pixels/s)')
    plt.title('Velocity and Fixation Periods')
    plt.legend()
    
    # Print statistics
    print(f"Number of velocity-based fixations: {len(fixations_vel)}")
    print(f"Number of dispersion-based fixations: {len(fixations_disp)}")
    
    plt.show()

if __name__ == "__main__":
    filepath = "database/raw_eyetracking/subj-01_group-1_role-Operator_eye_tracking_20241210094555.csv"
    plot_velocity_and_fixations(filepath, marker=113)

