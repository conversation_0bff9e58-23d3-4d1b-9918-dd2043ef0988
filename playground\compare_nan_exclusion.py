import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from tools.proc import (
    calculate_velocity,
    saccade_detection_velocity_with_direction,
    filter_saccades_near_nan
)

def compare_nan_exclusion_levels(filepath, marker=113):
    """Compare saccade detection with different NaN exclusion buffer sizes."""
    # Read data
    df = pd.read_csv(filepath, sep=";")
    df = df[df['Marker'] == marker]
    
    x = df["Screen_X_filtered"].to_numpy()
    y = df["Screen_Y_filtered"].to_numpy()
    t = df["Timestamp"].to_numpy()
    
    # the time is in seconds
    t_rel = (t - t[0]) 
    
    # Calculate velocities
    velocities = calculate_velocity(x, y, t)
    
    # Define buffer sizes to compare
    buffer_sizes = [0, 5, 10, 20]
    
    # Detect saccades with different buffer sizes
    saccades_list = []
    for buffer in buffer_sizes:
        if buffer == 0:
            # No filtering
            saccades = saccade_detection_velocity_with_direction(
                x, y, velocities, v_threshold=500, filter_near_nan=False
            )
        else:
            # With filtering
            saccades = saccade_detection_velocity_with_direction(
                x, y, velocities, v_threshold=500, filter_near_nan=True, nan_buffer=buffer
            )
        saccades_list.append(saccades)
    
    # Count NaN regions
    is_nan = np.isnan(x) | np.isnan(y)
    nan_regions = []
    if np.any(is_nan):
        nan_starts = np.where(np.diff(np.concatenate(([0], is_nan.astype(int)))) == 1)[0]
        nan_ends = np.where(np.diff(np.concatenate((is_nan.astype(int), [0]))) == -1)[0]
        nan_regions = list(zip(nan_starts, nan_ends))
    
    # Plot results - 2 rows (X and Y) for each buffer size
    fig, axes = plt.subplots(len(buffer_sizes), 2, figsize=(15, 5*len(buffer_sizes)), sharex=True)
    
    for i, (buffer, saccades) in enumerate(zip(buffer_sizes, saccades_list)):
        ax_x = axes[i, 0]  # X coordinate plot
        ax_y = axes[i, 1]  # Y coordinate plot
        
        # Plot X coordinates over time
        ax_x.plot(t_rel, x, 'gray', alpha=0.5, linewidth=0.5)
        
        # Plot Y coordinates over time
        ax_y.plot(t_rel, y, 'gray', alpha=0.5, linewidth=0.5)
        
        # Highlight NaN regions
        for start, end in nan_regions:
            ax_x.axvspan(t_rel[start], t_rel[min(end, len(t_rel)-1)], color='red', alpha=0.1)
            ax_y.axvspan(t_rel[start], t_rel[min(end, len(t_rel)-1)], color='red', alpha=0.1)
        
        # Plot saccades
        for j, (start, end) in enumerate(saccades):
            # Plot saccade in X coordinate
            ax_x.plot(t_rel[start:end+1], x[start:end+1], 'b-', linewidth=1.5)
            
            # Plot saccade in Y coordinate
            ax_y.plot(t_rel[start:end+1], y[start:end+1], 'b-', linewidth=1.5)
            
            # Add saccade number at midpoint
            mid_idx = start + (end - start) // 2
            ax_x.text(t_rel[mid_idx], x[mid_idx], str(j), color='blue', fontsize=8)
            ax_y.text(t_rel[mid_idx], y[mid_idx], str(j), color='blue', fontsize=8)
        
        # Add buffer visualization for non-zero buffers
        if buffer > 0:
            for start, end in nan_regions:
                buffer_start = max(0, start - buffer)
                buffer_end = min(len(t_rel) - 1, end + buffer)
                
                ax_x.axvspan(t_rel[buffer_start], t_rel[start], color='orange', alpha=0.1)
                ax_x.axvspan(t_rel[min(end, len(t_rel)-1)], t_rel[buffer_end], color='orange', alpha=0.1)
                
                ax_y.axvspan(t_rel[buffer_start], t_rel[start], color='orange', alpha=0.1)
                ax_y.axvspan(t_rel[min(end, len(t_rel)-1)], t_rel[buffer_end], color='orange', alpha=0.1)
        
        buffer_text = "No filtering" if buffer == 0 else f"Buffer: {buffer} samples"
        ax_x.set_title(f'X Coordinate - {buffer_text} ({len(saccades)} saccades)')
        ax_y.set_title(f'Y Coordinate - {buffer_text} ({len(saccades)} saccades)')
        
        ax_x.set_ylabel('X position (pixels)')
        ax_y.set_ylabel('Y position (pixels)')
        
        ax_x.grid(alpha=0.3)
        ax_y.grid(alpha=0.3)
    
    # Add x-axis label to bottom plots
    axes[-1, 0].set_xlabel('Time (seconds)')
    axes[-1, 1].set_xlabel('Time (seconds)')
    
    # Print statistics
    print("Saccade counts with different NaN exclusion buffers:")
    for buffer, saccades in zip(buffer_sizes, saccades_list):
        buffer_text = "No filtering" if buffer == 0 else f"Buffer: {buffer} samples"
        print(f"  {buffer_text}: {len(saccades)} saccades")
    
    plt.tight_layout()
    plt.show()
    
    return saccades_list

if __name__ == "__main__":
    filepath = "database/raw_eyetracking/subj-01_group-1_role-Operator_eye_tracking_20241210094555.csv"
    
    # Compare saccade detection with different NaN exclusion levels
    saccades_list = compare_nan_exclusion_levels(filepath, marker=143)
