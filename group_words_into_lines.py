import json
import cv2
import numpy as np
import os
from collections import defaultdict

def load_bounding_boxes(file_path="database/bounding_boxes/Bounding_Boxes.json"):
    """Load paragraph bounding boxes from JSON file."""
    with open(file_path, "r") as f:
        return json.load(f)

def load_word_bounding_boxes(file_path="database/bounding_boxes/Word_Bounding_Boxes.json"):
    """Load word bounding boxes from JSON file."""
    with open(file_path, "r") as f:
        return json.load(f)

def load_image_for_marker(marker):
    """
    Lädt das passende Bild für einen gegebenen Markerwert basierend auf den Codes.
    """
    scenario = {"1": 'a', "2": 'b', "3": 'c', "4": 'd', "5": 'e', "6": 'f'}
    ti_type = {"1": "system_goal", "2": "system_type", "3": "data", "4": "control"}

    marker_str = str(marker).zfill(3)  # <PERSON><PERSON><PERSON><PERSON>, dass der Marker dreistellig ist
    scenario_key, ti_key, page = marker_str[0], marker_str[1], marker_str[2]

    if scenario_key in scenario and ti_key in ti_type:
        image_filename = f"database/ti_png/{scenario[scenario_key]}_{ti_type[ti_key]}_{page}.png"
        if os.path.exists(image_filename):
            return cv2.imread(image_filename)
    return cv2.imread(f"database/ti_png/default.png")

def group_words_into_lines(word_boxes, y_threshold=10):
    """Group word boxes into lines based on similar y-coordinates.
    
    Args:
        word_boxes: List of word bounding boxes
        y_threshold: Maximum y-difference to consider words on the same line
        
    Returns:
        List of lines, where each line is a list of word boxes
    """
    if not word_boxes:
        return []
    
    # Sort words by y-coordinate (top_left.y)
    sorted_words = sorted(word_boxes, key=lambda box: box["top_left"]["y"])
    
    lines = []
    current_line = [sorted_words[0]]
    current_y = sorted_words[0]["top_left"]["y"]
    
    for word in sorted_words[1:]:
        word_y = word["top_left"]["y"]
        
        # If this word is on the same line (y-coordinate within threshold)
        if abs(word_y - current_y) <= y_threshold:
            current_line.append(word)
        else:
            # Sort words in the current line by x-coordinate
            current_line = sorted(current_line, key=lambda box: box["top_left"]["x"])
            lines.append(current_line)
            
            # Start a new line
            current_line = [word]
            current_y = word_y
    
    # Add the last line if not empty
    if current_line:
        current_line = sorted(current_line, key=lambda box: box["top_left"]["x"])
        lines.append(current_line)
    
    return lines

def main():
    # Load bounding boxes
    paragraph_boxes = load_bounding_boxes()
    word_boxes = load_word_bounding_boxes()
    
    # Create output directory
    output_dir = "line_visualizations"
    os.makedirs(output_dir, exist_ok=True)
    
    # Create structure for line bounding boxes
    line_bounding_boxes = {"images": []}
    
    # Process each marker
    for word_marker_data in word_boxes["images"]:
        marker_id = word_marker_data["id"]
        print(f"Processing marker {marker_id}")
        
        # Find paragraph boxes for this marker
        paragraph_data = next((img for img in paragraph_boxes["images"] if img["id"] == marker_id), None)
        if not paragraph_data:
            print(f"No paragraph data found for marker {marker_id}")
            continue
        
        # Group words into lines
        lines = group_words_into_lines(word_marker_data["word_boxes"], y_threshold=20)
        
        # Create line bounding boxes
        marker_lines = []
        for line in lines:
            if not line:
                continue
                
            # Create a bounding box that encompasses all words in the line
            min_x = min(word["top_left"]["x"] for word in line)
            min_y = min(word["top_left"]["y"] for word in line)
            max_x = max(word["bottom_right"]["x"] for word in line)
            max_y = max(word["bottom_right"]["y"] for word in line)
            
            line_box = {
                "top_left": {"x": min_x, "y": min_y},
                "bottom_right": {"x": max_x, "y": max_y},
                "words": line
            }
            marker_lines.append(line_box)
        
        # Add to output structure
        line_bounding_boxes["images"].append({
            "id": marker_id,
            "line_boxes": marker_lines
        })
        
        # Load image for visualization
        img = load_image_for_marker(marker_id)
        if img is None:
            print(f"No image found for marker {marker_id}")
            continue
        
        # Create a copy for visualization
        vis_img = img.copy()
        
        # Draw paragraph boxes in blue
        for i, box in enumerate(paragraph_data["bounding_boxes"]):
            cv2.rectangle(vis_img, 
                         (box["top_left"]["x"], box["top_left"]["y"]),
                         (box["bottom_right"]["x"], box["bottom_right"]["y"]),
                         (255, 0, 0), 2)  # Blue
            # Add label
            cv2.putText(vis_img, f"P{i}", 
                       (box["top_left"]["x"], box["top_left"]["y"] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 0, 0), 2)
        
        # Draw word boxes in green
        for word in word_marker_data["word_boxes"]:
            cv2.rectangle(vis_img, 
                         (word["top_left"]["x"], word["top_left"]["y"]),
                         (word["bottom_right"]["x"], word["bottom_right"]["y"]),
                         (0, 255, 0), 3)  # Green
        
        # Draw line boxes in red
        for i, line in enumerate(marker_lines):
            cv2.rectangle(vis_img, 
                         (line["top_left"]["x"], line["top_left"]["y"]),
                         (line["bottom_right"]["x"], line["bottom_right"]["y"]),
                         (0, 0, 255), 2)  # Red
            # Add label
            cv2.putText(vis_img, f"L{i}", 
                       (line["top_left"]["x"] - 30, line["top_left"]["y"] + 15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Save visualization
        cv2.imwrite(f"{output_dir}/marker_{marker_id}_lines.png", vis_img)
        
        # Display the image (optional)
        cv2.imshow(f"Marker {marker_id} - Lines", vis_img)
        key = cv2.waitKey(0)
        if key == ord('q'):
            cv2.destroyAllWindows()
            break
        cv2.destroyWindow(f"Marker {marker_id} - Lines")
    
    # Save line bounding boxes to file
    with open("database/bounding_boxes/Line_Bounding_Boxes.json", "w") as f:
        json.dump(line_bounding_boxes, f, indent=2)
    
    print("Line bounding boxes saved to database/bounding_boxes/Line_Bounding_Boxes.json")

if __name__ == "__main__":
    main()