import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

def normalize_and_plot_metrics():
    """
    Load data from results_dataframe_format.csv, normalize metrics using z-scores
    across subjects, and plot comparisons between original and normalized values.
    """
    # Load the data
    input_file = "results/results_dataframe_format.csv"
    output_dir = "results/normalized_plots"
    os.makedirs(output_dir, exist_ok=True)
    
    # Read the CSV file
    df = pd.read_csv(input_file)
    
    # Replace infinities and zeros
    df = df.replace([np.inf, -np.inf], np.nan)
    df = df.replace(0, 0.001)
    df = df.dropna()
    
    # Get metric columns (exclude non-metric columns)
    exclude_cols = ['subject_id', 'group', 'role', 'marker', 'scenario', 'ti_type', 'page']
    metrics = [col for col in df.columns if col not in exclude_cols]
    
    # Create a new dataframe to store normalized values
    df_normalized = df.copy()
    
    # Normalize each metric using z-score (across all subjects)
    for metric in metrics:
        df_normalized[metric] = (df[metric] - df[metric].mean()) / df[metric].std()
    
    # Save normalized data
    df_normalized.to_csv("results/normalized_dataframe.csv", index=False)
    print(f"Normalized data saved to results/normalized_dataframe.csv")
    
    # Select a few metrics to plot as examples
    example_metrics = ['pupil_diameter_avg', 
                       'fixation_length_avg', 
                       'saccade_velocity_avg',
                       'peak_saccade_velocity_avg', 
                       'fixation_frequency_vel', 
                       'blink_freq',
                       'scanpath_length_avg'
                       ]
    
    # Plot comparisons for each example metric
    for metric in example_metrics:
        # Create a 2x2 grid of plots (original and normalized, side by side)
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Plot 1: Original values by subject (top left)
        sns.boxplot(x='subject_id', y=metric, data=df, ax=axes[0, 0])
        axes[0, 0].set_title(f'Original values of {metric} by Subject')
        axes[0, 0].set_xlabel('Subject ID')
        axes[0, 0].set_ylabel(metric)
        
        # Plot 2: Normalized values by subject (top right)
        sns.boxplot(x='subject_id', y=metric, data=df_normalized, ax=axes[0, 1])
        axes[0, 1].set_title(f'Z-score normalized values of {metric} by Subject')
        axes[0, 1].set_xlabel('Subject ID')
        axes[0, 1].set_ylabel(f'{metric} (z-score)')
        
        # Plot 3: Original distribution (bottom left)
        axes[1, 0].hist(df[metric], bins=30, alpha=0.7)
        axes[1, 0].set_title(f'Distribution of original {metric}')
        axes[1, 0].set_xlabel('Value')
        axes[1, 0].set_ylabel('Frequency')
        
        # Plot 4: Normalized distribution (bottom right)
        axes[1, 1].hist(df_normalized[metric], bins=30, alpha=0.7)
        axes[1, 1].set_title(f'Distribution of normalized {metric}')
        axes[1, 1].set_xlabel('Value (z-score)')
        axes[1, 1].set_ylabel('Frequency')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'{metric}_comparison.png'))
        plt.close()
        
        print(f"Comparison plot saved for {metric}")
    
    # Create a summary plot showing before/after for multiple metrics
    fig, axes = plt.subplots(len(example_metrics), 2, figsize=(15, 4*len(example_metrics)))
    
    for i, metric in enumerate(example_metrics):
        # Original data
        axes[i, 0].violinplot([df[df['subject_id'] == subj][metric] for subj in df['subject_id'].unique()])
        axes[i, 0].set_title(f'Original {metric}')
        axes[i, 0].set_ylabel(metric)
        axes[i, 0].set_xticks(range(1, len(df['subject_id'].unique())+1))
        axes[i, 0].set_xticklabels(df['subject_id'].unique())
        
        # Normalized data
        axes[i, 1].violinplot([df_normalized[df_normalized['subject_id'] == subj][metric] 
                              for subj in df_normalized['subject_id'].unique()])
        axes[i, 1].set_title(f'Normalized {metric}')
        axes[i, 1].set_ylabel(f'{metric} (z-score)')
        axes[i, 1].set_xticks(range(1, len(df_normalized['subject_id'].unique())+1))
        axes[i, 1].set_xticklabels(df_normalized['subject_id'].unique())
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'metrics_summary_comparison.png'))
    plt.close()
    
    print("Summary comparison plot saved")

if __name__ == "__main__":
    import seaborn as sns
    normalize_and_plot_metrics()
