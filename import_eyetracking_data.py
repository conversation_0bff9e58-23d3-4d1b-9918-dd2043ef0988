import os
import shutil
import glob

def import_eyetracking_data():
    # Define source and destination directories
    source_dir = "database/Datensicherung Eye-Tracking Studie"
    dest_dir = "database/raw_eyetracking"
    
    # Create destination directory if it doesn't exist
    os.makedirs(dest_dir, exist_ok=True)
    
    # Get all files and directories in the source directory
    all_items = glob.glob(f"{source_dir}/**/*", recursive=True)
    
    # Filter for eye-tracking data files (assuming they have specific extensions)
    eyetracking_files = [f for f in all_items if f.endswith(('.csv')) 
                         and not '_questionnaires_' in f]
    
    # Process each file
    for file_path in eyetracking_files:
        # Get the filename
        filename = os.path.basename(file_path)
        dest_path = os.path.join(dest_dir, filename)
        
        # Check if file already exists in destination
        if not os.path.exists(dest_path):
            print(f"Moving {filename} to {dest_dir}")
            shutil.copy2(file_path, dest_path)
        else:
            print(f"File {filename} already exists in destination, skipping")
    
    print("Eye-tracking data import complete")

if __name__ == "__main__":
    import_eyetracking_data()