import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import savgol_filter
from tools.proc import (
    apply_wiener_filter,
    calculate_velocity,
    load_image_for_marker,
    fixation_detection_velocity
)

def compare_filtering_methods(filepath, marker=113, window_size=7):
    """
    Compare different filtering methods on eye tracking data.
    
    Args:
        filepath (str): Path to the eye tracking CSV file
        marker (int): Marker to filter data for
        window_size (int): Window size for filters
    """
    # Read data
    df = pd.read_csv(filepath, sep=";")
    df = df[df['Marker'] == marker]
    
    if len(df) == 0:
        print(f"No data found for marker {marker}")
        return
    
    # Get necessary data
    x = df["Screen_X_filtered"].to_numpy()
    y = df["Screen_Y_filtered"].to_numpy()
    t = df["Timestamp"].to_numpy()
    pupil_left = df["Diameter_left"].to_numpy() * 2  # Convert radius to diameter
    pupil_right = df["Diameter_right"].to_numpy() * 2
    
    # Convert to relative time
    t_rel = (t - t[0])
    
    # Apply different filters
    # 1. Wiener filter
    x_wiener = apply_wiener_filter(x, window_size)
    y_wiener = apply_wiener_filter(y, window_size)
    pupil_left_wiener = apply_wiener_filter(pupil_left, window_size)
    pupil_right_wiener = apply_wiener_filter(pupil_right, window_size)
    
    # 2. Savitzky-Golay filter
    # Create masks for non-NaN values
    x_mask = ~np.isnan(x)
    y_mask = ~np.isnan(y)
    pl_mask = ~np.isnan(pupil_left)
    pr_mask = ~np.isnan(pupil_right)
    
    # Initialize filtered arrays with NaNs
    x_savgol = np.full_like(x, np.nan)
    y_savgol = np.full_like(y, np.nan)
    pupil_left_savgol = np.full_like(pupil_left, np.nan)
    pupil_right_savgol = np.full_like(pupil_right, np.nan)
    
    # Apply Savitzky-Golay filter only to non-NaN values
    if np.sum(x_mask) > window_size:
        x_savgol[x_mask] = savgol_filter(x[x_mask], window_size, 3)
    if np.sum(y_mask) > window_size:
        y_savgol[y_mask] = savgol_filter(y[y_mask], window_size, 3)
    if np.sum(pl_mask) > window_size:
        pupil_left_savgol[pl_mask] = savgol_filter(pupil_left[pl_mask], window_size, 3)
    if np.sum(pr_mask) > window_size:
        pupil_right_savgol[pr_mask] = savgol_filter(pupil_right[pr_mask], window_size, 3)
    
    velocity = calculate_velocity(x, y, t)
    velocity_wiener = calculate_velocity(x_wiener, y_wiener, t)
    velocity_savgol = calculate_velocity(x_savgol, y_savgol, t)

    # Detect fixations on filtered data
    fixations_original = fixation_detection_velocity(velocity, t, 500)
    fixations_wiener = fixation_detection_velocity(velocity_wiener, t, 500)
    fixations_savgol = fixation_detection_velocity(velocity_savgol, t, 500)
    
    # Create binary arrays for fixation periods
    fix_original = np.zeros_like(t_rel)
    fix_wiener = np.zeros_like(t_rel)
    fix_savgol = np.zeros_like(t_rel)
    
    for start, end in fixations_original:
        fix_original[start:end] = 1
    
    for start, end in fixations_wiener:
        fix_wiener[start:end] = 1
    
    for start, end in fixations_savgol:
        fix_savgol[start:end] = 1
    
    # Create figure with subplots
    fig, axes = plt.subplots(3, 1, figsize=(15, 15), sharex=True)
    
    # Plot X coordinates
    axes[0].plot(t_rel, x, 'gray', alpha=0.5, label='Original')
    axes[0].plot(t_rel, x_wiener, 'r-', label='Wiener Filter')
    axes[0].plot(t_rel, x_savgol, 'g-', label='Savitzky-Golay Filter')
    
    # Add fixation blocks for X plot
    y_min, y_max = axes[0].get_ylim()
    height = y_max - y_min
    
    axes[0].fill_between(t_rel, y_min, y_max, where=fix_original==1, 
                        color='blue', alpha=0.1, label='Original Fixations')
    axes[0].fill_between(t_rel, y_min, y_max, where=fix_wiener==1, 
                        color='red', alpha=0.1, label='Wiener Fixations')
    axes[0].fill_between(t_rel, y_min, y_max, where=fix_savgol==1, 
                        color='green', alpha=0.1, label='Savgol Fixations')
    
    axes[0].set_ylabel('X Coordinate (pixels)')
    axes[0].set_title(f'Comparison of Filtering Methods - Marker {marker}')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Plot Y coordinates
    axes[1].plot(t_rel, y, 'gray', alpha=0.5, label='Original')
    axes[1].plot(t_rel, y_wiener, 'r-', label='Wiener Filter')
    axes[1].plot(t_rel, y_savgol, 'g-', label='Savitzky-Golay Filter')
    
    # Add fixation blocks for Y plot
    y_min, y_max = axes[1].get_ylim()
    
    axes[1].fill_between(t_rel, y_min, y_max, where=fix_original==1, 
                        color='blue', alpha=0.1, label='Original Fixations')
    axes[1].fill_between(t_rel, y_min, y_max, where=fix_wiener==1, 
                        color='red', alpha=0.1, label='Wiener Fixations')
    axes[1].fill_between(t_rel, y_min, y_max, where=fix_savgol==1, 
                        color='green', alpha=0.1, label='Savgol Fixations')
    
    axes[1].set_ylabel('Y Coordinate (pixels)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # Plot pupil diameter (average of left and right)
    axes[2].plot(t_rel, (pupil_left + pupil_right)/2, 'gray', alpha=0.5, label='Original')
    axes[2].plot(t_rel, (pupil_left_wiener + pupil_right_wiener)/2, 'r-', label='Wiener Filter')
    axes[2].plot(t_rel, (pupil_left_savgol + pupil_right_savgol)/2, 'g-', label='Savitzky-Golay Filter')
    
    # Add fixation blocks for pupil plot
    y_min, y_max = axes[2].get_ylim()
    
    axes[2].fill_between(t_rel, y_min, y_max, where=fix_original==1, 
                        color='blue', alpha=0.1, label='Original Fixations')
    axes[2].fill_between(t_rel, y_min, y_max, where=fix_wiener==1, 
                        color='red', alpha=0.1, label='Wiener Fixations')
    axes[2].fill_between(t_rel, y_min, y_max, where=fix_savgol==1, 
                        color='green', alpha=0.1, label='Savgol Fixations')
    
    axes[2].set_ylabel('Pupil Diameter (mm)')
    axes[2].set_xlabel('Time (seconds)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Create a second figure to compare gaze paths
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # Load image for background
    image = load_image_for_marker(marker)
    if image is not None:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        for ax in axes:
            ax.imshow(image)
    
    # Plot original gaze path
    axes[0].plot(x, y, 'gray', alpha=0.5, linewidth=0.5)
    axes[0].set_title(f'Original Gaze Path ({len(fixations_original)} fixations)')
    
    # Plot Wiener filtered gaze path
    axes[1].plot(x_wiener, y_wiener, 'r-', alpha=0.7, linewidth=0.5)
    axes[1].set_title(f'Wiener Filtered Gaze Path ({len(fixations_wiener)} fixations)')
    
    # Plot Savitzky-Golay filtered gaze path
    axes[2].plot(x_savgol, y_savgol, 'g-', alpha=0.7, linewidth=0.5)
    axes[2].set_title(f'Savitzky-Golay Filtered Gaze Path ({len(fixations_savgol)} fixations)')
    
    # Plot fixations
    for i, (start, end) in enumerate(fixations_original):
        fix_x = np.nanmean(x[start:end])
        fix_y = np.nanmean(y[start:end])
        axes[0].plot(fix_x, fix_y, 'bo', alpha=0.5, markersize=8)
    
    for i, (start, end) in enumerate(fixations_wiener):
        fix_x = np.nanmean(x_wiener[start:end])
        fix_y = np.nanmean(y_wiener[start:end])
        axes[1].plot(fix_x, fix_y, 'bo', alpha=0.5, markersize=8)
    
    for i, (start, end) in enumerate(fixations_savgol):
        fix_x = np.nanmean(x_savgol[start:end])
        fix_y = np.nanmean(y_savgol[start:end])
        axes[2].plot(fix_x, fix_y, 'bo', alpha=0.5, markersize=8)
    
    # Print statistics
    print(f"Number of fixations (Original): {len(fixations_original)}")
    print(f"Number of fixations (Wiener): {len(fixations_wiener)}")
    print(f"Number of fixations (Savitzky-Golay): {len(fixations_savgol)}")
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    # Add cv2 import here to avoid issues if it's not used in the module scope
    import cv2
    
    filepath = "database/raw_eyetracking/subj-01_group-1_role-Operator_eye_tracking_20241210094555.csv"
    
    # Compare filtering methods for different markers
    for marker in [113, 121, 132]:
        compare_filtering_methods(filepath, marker=marker, window_size=7)
