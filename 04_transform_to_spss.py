import pandas as pd
import numpy as np
def transform_to_spss_format():
    # Read the original data
    df = pd.read_csv("results/normalized_dataframe.csv")
    
    # List of metrics to transform
    metrics = [
        'pupil_diameter_avg', 'pupil_diameter_var', 'pupil_diameter_kurtosis',
        'pupil_diameter_skewness', 'fixation_length_avg', 'fixation_length_var',
        'fixation_length_kurtosis', 'fixation_length_skewness',
        'saccade_velocity_avg', 'saccade_velocity_var', 'saccade_velocity_kurtosis',
        'saccade_velocity_skewness', 'peak_saccade_velocity_avg',
        'peak_saccade_velocity_var', 'peak_saccade_velocity_kurtosis',
        'peak_saccade_velocity_skewness', 'fixation_frequency_vel',
        'saccade_frequency_vel', 'blink_freq', 'marker_length', 'scanpath_length',
        'scanpath_length_avg', 'mean_hull_size', 'max_hull_size',
        'fixation_length_vel_norm', 'scanpath_length_norm', 'mean_hull_size_norm',
        'max_hull_size_norm'
    ]
    
       


    # Create a dictionary to store the transformed data
    spss_data = {}
    
    # Add subject information columns
    spss_data['subject_id'] = df['subject_id'].unique()
                    
    # For each metric, create columns for all scenario-ti_type-page combinations
    for metric in metrics:
        for scenario in df['scenario'].unique():
            for ti_type in df['ti_type'].unique():
                for page in df['page'].unique():
                    subject_values = []
                    for subject in df['subject_id'].unique():
                        if subject == 4 or subject == 21:
                            continue
                        # Create column name in format: metric_s_t_p
                        col_name = f"{metric}_s{scenario}_t{ti_type}_p{page}"
                        
                        # Get the values for this combination
                        value = df[
                            (df['subject_id']==subject) &
                            (df['scenario'] == scenario) & 
                            (df['ti_type'] == ti_type) & 
                            (df['page'] == page)
                        ][metric].values
                        if value.shape[0]==0:
                            value = 0
                        else:
                            value = value[0]

                        subject_values.append(value)
                    print(col_name, len(subject_values))
                    spss_data[col_name] = subject_values

    # Create the final dataframe
    spss_df = pd.DataFrame(spss_data)
    
    # Ensure subject_id is the first column
    cols = ['subject_id'] + [col for col in spss_df.columns 
                                             if col not in ['subject_id']]
    spss_df = spss_df[cols]
    
    # Save to CSV
    spss_df.to_csv("results/spss_format_metrics.csv", index=False, na_rep=0)
    print(f"Transformed data saved to results/spss_format_metrics.csv")
    print(f"Number of columns: {len(spss_df.columns)}")
    print(f"Number of subjects: {len(spss_df)}")

if __name__ == "__main__":
    transform_to_spss_format()