# Eye Tracking Data Analysis Pipeline

This repository contains a pipeline for processing and analyzing eye tracking data. The pipeline extracts various metrics from raw eye tracking data, processes them, and prepares them for statistical analysis.

## Pipeline Overview

1. **Data Collection**: Raw eye tracking data is stored in `database/raw_eyetracking`
2. **Data quality check**: `01_check_marker_data_quality.py`
3. **Data Processing**: `02_process_metrics.py` processes the raw data to extract metrics
4. **Visualization**: `03_plot_metrics.py` creates visualizations of the metrics
5. **Statistical Analysis**: `04_transform_to_spss.py` prepares data for statistical analysis with columns e.g.: "pupil_diameter_s5_t1_p1"

## Experimental Design Variables

The eye tracking data is organized according to three within-subject variables in a reading task:

- **Scenario (s)**: Represents different text topics that participants read. Each scenario presents a unique subject matter with varying content complexity.
- **TI Type (t)**: Refers to different text presentation formats or transparency interfaces incorporated into the reading materials.
- **Page (p)**: Indicates the paragraph number within each text, allowing for analysis of reading patterns as participants progress through the content.

These variables are encoded in the marker IDs, where the first digit represents the scenario (topic), the second digit represents the TI type (presentation format), and the third digit represents the paragraph number.

## Workload Hypothesis

We hypothesize that several eye tracking metrics will reflect changes in reading workload across different text conditions:

- **Pupil diameter metrics**: Increased pupil diameter is expected to correlate with higher cognitive load during reading of complex or unfamiliar topics.
- **Fixation metrics**: Longer fixation durations and higher fixation counts may indicate increased text processing difficulty and comprehension effort.
- **Saccade metrics**: Shorter saccades and more regressions (backward saccades) might reflect reading difficulties and text complexity.
- **Blink metrics**: Reduced blink frequency often accompanies increased concentration during challenging reading passages.
- **Spatial metrics**: More linear scanpath patterns suggest fluent reading, while irregular patterns with larger hull sizes may indicate difficulty integrating information across the text.

## Metrics Explanation

The following metrics are calculated from the filtered eye tracking data, filtered by the eyetracker itself.:

All Metrics are a single scalar value per marker and subject. NaNs are omitted from the calculations.

### Pupil Diameter Metrics

- **pupil_diameter_avg**: Mean pupil diameter (in mm)
  - Calculated from `Diameter_left` and `Diameter_right` columns (multiplied by 2 to convert from radius to diameter)
  - Formula: `mean(pupil_left + pupil_right)`

- **pupil_diameter_var**: Variance of pupil diameter
  - Formula: `var(pupil_diameter_per_marker[marker])`

- **pupil_diameter_kurtosis**: Kurtosis of pupil diameter distribution
  - Formula: Uses `scipy.stats.kurtosis` on pupil diameter values

- **pupil_diameter_skewness**: Skewness of pupil diameter distribution
  - Formula: Uses `scipy.stats.skew` on pupil diameter values

### Fixation Metrics

- **fixation_length_avg**: Average duration of fixations (in seconds)
  - Calculated from fixations detected using velocity threshold
  - Formula: `mean(end_time - start_time)` for all fixations

- **fixation_length_var**: Variance of fixation durations
  - Formula: `var(fix_length_per_marker[marker])`

- **fixation_length_kurtosis**: Kurtosis of fixation duration distribution
  - Formula: Uses `scipy.stats.kurtosis` on fixation durations

- **fixation_length_skewness**: Skewness of fixation duration distribution
  - Formula: Uses `scipy.stats.skew` on fixation durations

- **fixation_frequency_vel**: Number of fixations per second
  - Formula: `count(fixations) / total_time` (multiplied by 60 for per minute)

- **fixation_length_vel_norm**: Fixation duration normalized by marker length
  - Formula: `fixation_length_avg / marker_length`

### Saccade Metrics
here the `saccade_velocities` contain the average velocity of each saccade for that marker and `peak_saccade_velocities` the max velocity for each saccade in that marker

- **saccade_velocity_avg**: Average velocity of saccades (in pixels/second)
  - Calculated from velocities during saccades
  - Formula: `mean(saccade_velocities[marker])`

- **saccade_velocity_var**: Variance of saccade velocities
  - Formula: `var(saccade_velocities[marker])`

- **saccade_velocity_kurtosis**: Kurtosis of saccade velocity distribution
  - Formula: Uses `scipy.stats.kurtosis` on saccade velocities

- **saccade_velocity_skewness**: Skewness of saccade velocity distribution
  - Formula: Uses `scipy.stats.skew` on saccade velocities

- **peak_saccade_velocity_avg**: Average of peak velocities for each saccade
  - Formula: `mean(peak_saccade_velocities[marker])` for each saccade

- **peak_saccade_velocity_var**: Variance of peak saccade velocities
  - Formula: `var(peak_saccade_velocities[marker])`

- **peak_saccade_velocity_kurtosis**: Kurtosis of peak saccade velocity distribution
  - Formula: Uses `scipy.stats.kurtosis` on peak saccade velocities

- **peak_saccade_velocity_skewness**: Skewness of peak saccade velocity distribution
  - Formula: Uses `scipy.stats.skew` on peak saccade velocities

- **saccade_frequency_vel**: Number of saccades per second
  - Formula: `count(saccades) / total_time`

### Blink Metrics

- **blink_freq**: Number of blinks per second
  - Detected from gaps in eye tracking data
  - Formula: `count(blinks) / marker_length` (multiplied by 60 for per minute)
  - Parameters: min_blink_duration=0.07s, max_blink_duration=0.4s

### Spatial Metrics

- **scanpath_length**: Total length of eye movement path (in pixels)
  - Formula: Sum of Euclidean distances between consecutive fixations
  - `sum(sqrt((fix_x[i+1] - fix_x[i])² + (fix_y[i+1] - fix_y[i])²))`
  
- **scanpath_length_avg**: Average length between consecutive fixations
  - Formula: `mean(scanpath_length[marker])`

- **scanpath_length_norm**: Scanpath length normalized by marker length
  - Formula: `scanpath_length / marker_length`

- **mean_hull_size**: Average size of convex hulls around fixation points
  - Formula: `mean(hull_sizes)` where hull_sizes are calculated using convex hull algorithm

- **max_hull_size**: Maximum size of convex hulls around fixation points
  - Formula: `max(hull_sizes)`

- **mean_hull_size_norm**: Mean hull size normalized by marker length
  - Formula: `mean_hull_size / marker_length`

- **max_hull_size_norm**: Maximum hull size normalized by marker length
  - Formula: `max_hull_size / marker_length`

### Time Metrics

- **marker_length**: Duration of time spent on a marker (in seconds)
  - Formula: `max(t[markers==marker]) - min(t[markers==marker])`

## Velocity Calculation

Velocity is calculated as:
```
v = sqrt((x[i+1] - x[i])² + (y[i+1] - y[i])²) / (t[i+1] - t[i])
```

Where:
- x, y are screen coordinates
- t is timestamp in seconds

## Fixation Detection

Fixations are detected using a velocity-based algorithm:
1. Calculate point-to-point velocity
2. Apply threshold (500 pixels/second)
3. Consecutive points below threshold are grouped as fixations

## Saccade Detection

Saccades are detected using:
1. Velocity threshold (550 pixels/second)
2. Direction change threshold (45 degrees)
3. Filtering near NaN values (buffer=10)

## Heatmap Generation

Heatmaps visualize gaze density using:
1. Binning of gaze points
2. Gaussian smoothing (sigma=20)
3. Normalization to [0,1] range...

## Further Work

### Fixation Position Correction

Currently, the fixation positions are not corrected for vertical drift, which limits the accuracy of text-specific analyses. Several drift correction algorithms have been implemented in `drift_algorithms.py` and need to be evaluated to determine the most effective approach for our reading task data. These include:

- Attach algorithm
- Chain algorithm
- Cluster algorithm
- Compare algorithm
- Merge algorithm
- Regress algorithm
- Segment algorithm
- Split algorithm
- Stretch algorithm
- Warp algorithm

The main challenges for accurate drift correction include:
1. Fixations to the header in each marker
2. Fixations below the text where navigation buttons were displayed
3. Distinguishing between actual reading fixations and non-reading fixations

Once fixation positions are properly corrected, word-specific metrics can be extracted, allowing for more fine-grained analysis of reading patterns at the word level.

### Scanpath Linearity Metrics

Scanpath linearity metrics have not yet been implemented. These metrics would provide valuable information about reading fluency and comprehension difficulties. Potential metrics include:

- Regression ratio (proportion of backward saccades)
- Reading progression score (how linear the reading pattern is)
- Line transition accuracy (how precisely readers move from the end of one line to the beginning of the next)

### First Fixation Normalization

Another limitation of the current experimental design is the absence of a fixation cross before each marker presentation. This means that the first fixation for each marker is not normalized, potentially introducing variability in the initial gaze position. we might want to discard the first fixation of each marker
