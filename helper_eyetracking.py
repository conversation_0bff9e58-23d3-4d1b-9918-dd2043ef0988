# -*- coding: utf-8 -*-
"""
Created on Thu Oct  7 10:00:43 2021

@author: hirning
"""

import pandas as pd
import numpy as np
from scipy.interpolate import PchipInterpolator as pchip


def outlier_removal_in_col(data, col = 'RIGHT_PUPIL_SIZE', lower_perc= 0.05, upper_perc= 0.95):
    upper_boundary = data[col].quantile(upper_perc)
    lower_boundary = data[col].quantile(lower_perc)
    data.loc[(data[col] > upper_boundary) & (data[col] < lower_boundary), col] = np.nan
    return data

def convert_pupil_mm(df, scaling, col = 'RIGHT_PUPIL_SIZE', area = True):
    if area == True: 
        df.loc[:,col] = scaling * np.sqrt(df[col])
    else: 
        print('ERROR: Other scalings than AREA are not implemented - no changes in df!!!')
    return df

def clean_pupil_physio(df, col = 'RIGHT_PUPIL_SIZE', lower_boundery = 1.5, upper_boundery = 9):
    df.loc[(df[col] > upper_boundery) | (df[col] < lower_boundery), col] = np.nan
    return df

def get_blink_start_end(data, blockstart, blockend, blink_col = 'RIGHT_IN_BLINK'):
    # Find the start of blinks: where the current value is 1 and the previous is 0
    onset_idx = data.index[
        (data[blink_col] == 1) & (data[blink_col].shift(1) == 0)].tolist()
    # Use list comprehension to apply the condition to each item in onset_idx
    onset_idx = [idx for idx in onset_idx if blockstart <= idx <= blockend]

    # Find the end of blinks: where the current value is 0 and the previous is 1
    # Corrected to ensure the condition is applied properly
    offset_idx = data.index[
        (data[blink_col] == 0) & (data[blink_col].shift(1) == 1)].tolist()
    # Use list comprehension to apply the condition to each item in offset_idx
    offset_idx = [idx for idx in offset_idx if blockstart <= idx <= blockend]

    # Accounting for edge cases where the DataFrame starts or ends with a blink
    n_blinks = int(min(len(onset_idx), len(offset_idx)))
    onset_idx = onset_idx[:n_blinks]
    offset_idx = offset_idx[:n_blinks]
    assert len(onset_idx) == len(offset_idx)
    return onset_idx, offset_idx, n_blinks

def pchip_interpolate(df, col, onset, offset, lag, max_samples_to_interpolate):
    pre_onset = np.max([0, onset - lag])
    post_offset = np.min([offset + lag, len(df[col]) - 1])
    #correct onsets and offsets for nans
    try:
        onset, offset = df.loc[onset:offset, col].dropna().index[0], df.loc[onset:offset, col].dropna().index[-1]
    except:
        #print('Failed pchip interpol due to no point within ', lag * 2 ,'samples in', col)
        #print('DF for ONSET - OFFSET without nans:', len(df.loc[onset:offset, col].dropna()))
        return df
    try:
        pre_onset, post_offset = df.loc[pre_onset:post_offset, col].dropna().index[0], df.loc[pre_onset:post_offset, col].dropna().index[-1]
    except:
        #print('Failed pchip interpol due to no point within ', lag * 2 ,'samples in', col)
        #print('DF for PREONSET - PREOFFSET without nans:', len(df.loc[pre_onset:post_offset, col].dropna()))
        return df
    # Ensure strictly increasing x_points with minimal adjustments
    onset = pre_onset + 1 if pre_onset >= onset else onset
    offset = max([onset + 1, offset])
    post_offset = max([offset+1, post_offset])
    x_points = np.array(sorted([pre_onset, onset, offset, post_offset]))

    if abs(post_offset - pre_onset) > max_samples_to_interpolate:
        return df
    if any(x_points < 0):
        return df
    if any(x_points >= len(df[col]) - 1):
        return df
    

    y_points = list(df.loc[x_points, col].values)
    if pd.isna(y_points).any():
        return df
    tck = pchip(x_points, y_points)
    segment = list(range(x_points[0], x_points[-1]))
    df.loc[x_points[0]:x_points[-1]-1, col] = tck(segment)
    return df
