import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from scipy.signal import savgol_filter

def plot_pupil_dilation(filepath, smooth_window=51, polynomial_order=3):
    """
    Plot pupil dilation from eye tracking data with smoothing.
    
    Args:
        filepath (str): Path to the eye tracking CSV file
        smooth_window (int): Window size for <PERSON><PERSON><PERSON>ky-<PERSON><PERSON> filter (must be odd)
        polynomial_order (int): Polynomial order for <PERSON><PERSON><PERSON>ky-<PERSON><PERSON> filter
    """
    # Read the CSV file
    df = pd.read_csv(filepath, sep=";")
    
    # Get timestamps and convert to relative time in seconds
    timestamps = df["Timestamp"].to_numpy()
    relative_time = (timestamps - timestamps[0]) 
    
    # Get pupil diameter data
    left_pupil = df["Diameter_left"].to_numpy() * 2 #it actually is radius, hence the *2
    right_pupil = df["Diameter_right"].to_numpy() * 2
    
    # Apply <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON> filter for smoothing
    left_pupil_smooth = savgol_filter(left_pupil, smooth_window, polynomial_order)
    right_pupil_smooth = savgol_filter(right_pupil, smooth_window, polynomial_order)
    
    # Calculate mean pupil size
    mean_pupil = (left_pupil_smooth + right_pupil_smooth) /2
    
    # Create the plot
    plt.figure(figsize=(15, 8))
    
    # Plot raw data with low opacity
    plt.plot(relative_time, left_pupil, 'b.', alpha=0.1, label='Left Eye (Raw)')
    plt.plot(relative_time, right_pupil, 'r.', alpha=0.1, label='Right Eye (Raw)')
    
    # Plot smoothed data
    plt.plot(relative_time, left_pupil_smooth, 'b-', label='Left Eye (Smoothed)')
    plt.plot(relative_time, right_pupil_smooth, 'r-', label='Right Eye (Smoothed)')
    plt.plot(relative_time, mean_pupil, 'g-', label='Mean (Smoothed)')
    
    # Add markers for different stimuli
    markers = df["Marker"].to_numpy()
    unique_markers = np.unique(markers[markers >= 100])  # Only valid markers
    
    # Plot vertical lines for marker changes
    prev_marker = markers[0]
    for i, marker in enumerate(markers):
        if marker != prev_marker and marker >= 100:
            plt.axvline(x=relative_time[i], color='gray', linestyle='--', alpha=0.3)
            plt.text(relative_time[i], plt.ylim()[1], f'M{marker}', 
                    rotation=90, verticalalignment='top')
        prev_marker = marker
    
    plt.grid(True, alpha=0.3)
    plt.xlabel('Time (seconds)')
    plt.ylabel('Pupil Diameter')
    plt.title('Pupil Dilation Over Time')
    plt.legend()
    
    # Show statistics in a text box
    stats_text = (
        f'Mean Left: {np.nanmean(left_pupil):.2f}\n'
        f'Mean Right: {np.nanmean(right_pupil):.2f}\n'
        f'Std Left: {np.nanstd(left_pupil):.2f}\n'
        f'Std Right: {np.nanstd(right_pupil):.2f}'
    )
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

def plot_blink_pupil_dilation(filepath, blink_min_samples=25, blink_max_samples=100, window_size=200):
    """
    Plot normalized pupil dilation around blinks (NaN sections).
    
    Args:
        filepath (str): Path to the eye tracking CSV file
        blink_min_samples (int): Minimum number of consecutive NaN samples to consider a blink
        blink_max_samples (int): Maximum number of consecutive NaN samples to consider a blink
        window_size (int): Number of samples to show before and after the blink
    """
    # Read the CSV file
    df = pd.read_csv(filepath, sep=";")
    
    # Get pupil diameter data
    left_pupil = df["Diameter_left"].to_numpy() * 2
    right_pupil = df["Diameter_right"].to_numpy() * 2
    
    # Find NaN sequences
    is_nan = np.isnan(left_pupil) | np.isnan(right_pupil)
    nan_starts = np.where(np.diff(is_nan.astype(int)) == 1)[0] + 1
    nan_ends = np.where(np.diff(is_nan.astype(int)) == -1)[0] + 1
    
    # Create figure
    plt.figure(figsize=(15, 8))
    
    # Plot aligned blinks
    blink_count = 0
    
    # Store normalized segments for averaging
    all_left_segments = []
    all_right_segments = []
    
    for start, end in zip(nan_starts, nan_ends):
        blink_length = end - start
        
        # Only consider blinks of appropriate length
        if blink_min_samples <= blink_length <= blink_max_samples:
            # Get window around blink
            window_start = max(0, start - window_size)
            window_end = min(len(left_pupil), end + window_size)
            
            # Get segments
            left_segment = left_pupil[window_start:window_end]
            right_segment = right_pupil[window_start:window_end]
            
            # Normalize by the mean of the first 50 samples (before blink)
            left_baseline = np.nanmean(left_segment[:50])
            right_baseline = np.nanmean(right_segment[:50])
            
            if not np.isnan(left_baseline) and not np.isnan(right_baseline):
                left_norm = left_segment / left_baseline
                right_norm = right_segment / right_baseline
                
                # Store segments for averaging
                all_left_segments.append(left_norm)
                all_right_segments.append(right_norm)
                
                # Get time relative to blink start
                x = np.arange(-window_size, window_end - window_start - window_size)
                
                # Plot normalized data
                plt.plot(x, left_norm, 'b-', alpha=0.1)
                plt.plot(x, right_norm, 'r-', alpha=0.1)
                
                blink_count += 1
                
                # Limit number of plotted blinks for clarity
                if blink_count >= 50:
                    break
    
    # Calculate and plot mean normalized response
    if all_left_segments and all_right_segments:
        # Find minimum length to align segments
        min_length = min(min(len(s) for s in all_left_segments), 
                        min(len(s) for s in all_right_segments))
        
        # Truncate all segments to minimum length
        all_left_segments = [s[:min_length] for s in all_left_segments]
        all_right_segments = [s[:min_length] for s in all_right_segments]
        
        # Calculate mean response
        mean_left = np.nanmean(all_left_segments, axis=0)
        mean_right = np.nanmean(all_right_segments, axis=0)
        
        # Plot mean response with thicker line
        x_mean = np.arange(-window_size, min_length - window_size)
        plt.plot(x_mean, mean_left, 'b-', linewidth=2, label='Mean Left Eye', alpha=0.8)
        plt.plot(x_mean, mean_right, 'r-', linewidth=2, label='Mean Right Eye', alpha=0.8)
    
    # Add vertical lines for blink start/end
    plt.axvline(x=0, color='gray', linestyle='--', alpha=0.5, label='Blink start')
    plt.axvline(x=blink_max_samples, color='gray', linestyle='--', alpha=0.5, label='Max blink end')
    
    plt.grid(True, alpha=0.3)
    plt.xlabel('Samples relative to blink start')
    plt.ylabel('Normalized Pupil Diameter')
    plt.title(f'Normalized Pupil Dilation Around Blinks (n={blink_count})\nBlue: Left Eye, Red: Right Eye')
    plt.legend()
    
    # Add shaded area for blink duration
    plt.axvspan(0, blink_max_samples, color='gray', alpha=0.1, label='Possible blink duration')
    
    # Add horizontal line at y=1 to show baseline
    plt.axhline(y=1, color='black', linestyle=':', alpha=0.3)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    filepath = r"database\raw_eyetracking\subj-14_group-2_role-Operator_eye_tracking_20241217150935.csv"
    plot_pupil_dilation(filepath)
