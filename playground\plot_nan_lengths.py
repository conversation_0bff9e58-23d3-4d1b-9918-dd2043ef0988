import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

def get_nan_lengths(x, y, t):
    """Calculate lengths of NaN sequences in the data"""
    # Combine x and y NaNs - if either is NaN, consider it a blink
    is_nan = np.logical_or(np.isnan(x), np.isnan(y))
    
    # Find where NaN sequences start and end
    nan_starts = np.where(np.logical_and(~is_nan[:-1], is_nan[1:]))[0]
    nan_ends = np.where(np.logical_and(is_nan[:-1], ~is_nan[1:]))[0]
    
    # Handle edge cases
    if len(nan_starts) == 0 and len(nan_ends) == 0:
        return np.array([])
    
    # If sequence starts with NaN, add start point
    if is_nan[0]:
        nan_starts = np.insert(nan_starts, 0, 0)
    
    # # If sequence ends with NaN, add end point
    # if is_nan[-1]:
    #     nan_ends = np.append(nan_ends, len(is_nan)-1)
    
    # Calculate lengths in seconds using timestamp differences
    lengths = []
    for start, end in zip(nan_starts, nan_ends):
        length = t[end + 1] - t[start]
        lengths.append(length)
    
    return np.array(lengths)

def main():
    data_dir = "database/raw_eyetracking"
    all_lengths = []
    
    # Process all files
    for filename in os.listdir(data_dir):
        if filename.endswith(".csv"):
            file_path = os.path.join(data_dir, filename)
            print(f"Processing {filename}...")
            
            # Read data
            df = pd.read_csv(file_path, sep=";")
            x = df["Screen_X_filtered"].to_numpy()
            y = df["Screen_Y_filtered"].to_numpy()
            t = df["Timestamp"].to_numpy()
            
            # Get NaN lengths for this file
            lengths = get_nan_lengths(x, y, t)
            all_lengths.extend(lengths)
    
    # Convert to numpy array
    all_lengths = np.array(all_lengths)
    
    # Create histogram
    plt.figure(figsize=(12, 6))
    plt.hist(all_lengths[all_lengths < 0.5], bins=100, edgecolor='black')
    plt.xlabel('NaN Gap Duration (seconds)')
    plt.ylabel('Frequency')
    plt.title('Distribution of NaN Gap Durations Across All Subjects')
    
    # Add vertical lines for common thresholds
    thresholds = [0.005, 0.05, 0.1, 0.4]
    colors = ['green', 'red', 'blue', 'purple']
    labels = ['5ms', '50ms', '100ms', '400ms']
    
    for threshold, color, label in zip(thresholds, colors, labels):
        plt.axvline(x=threshold, color=color, linestyle='--', alpha=0.6, 
                   label=f'{label} threshold')
    
    # Add statistics to plot (only for gaps < 1s)
    gaps_under_1s = all_lengths[all_lengths < 1]
    stats_text = (
        f'Gaps <1s: {len(gaps_under_1s)}\n'
        f'Mean: {np.mean(gaps_under_1s):.3f}s\n'
        f'Median: {np.median(gaps_under_1s):.3f}s\n'
        f'Std: {np.std(gaps_under_1s):.3f}s'
    )
    plt.text(0.98, 0.98, stats_text,
             transform=plt.gca().transAxes,
             verticalalignment='top',
             horizontalalignment='right',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    main()
