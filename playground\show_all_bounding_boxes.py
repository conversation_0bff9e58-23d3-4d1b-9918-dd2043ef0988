import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import json
import cv2
import matplotlib.pyplot as plt
from tools.proc import load_image_for_marker

def load_json_file(file_path):
    """Load data from a JSON file."""
    try:
        with open(file_path, "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"File not found: {file_path}")
        return {"images": []}


def load_image_for_marker(marker):
    """
    Lädt das passende Bild für einen gegebenen Markerwert basierend auf den Codes.
    """
    scenario = {"1": 'a', "2": 'b', "3": 'c', "4": 'd', "5": 'e', "6": 'f'}
    ti_type = {"1": "system_goal", "2": "system_type", "3": "data", "4": "control"}

    marker_str = str(marker).zfill(3)  # <PERSON><PERSON><PERSON><PERSON>, dass der Marker dreistellig ist
    scenario_key, ti_key, page = marker_str[0], marker_str[1], marker_str[2]

    if scenario_key in scenario and ti_key in ti_type:
        image_filename = f"database/ti_png/{scenario[scenario_key]}_{ti_type[ti_key]}_{page}.png"
        if os.path.exists(image_filename):
            return cv2.imread(image_filename)
    return cv2.imread(f"database/ti_png/default.png")

def main():
    # Load all bounding box data
    paragraph_boxes = load_json_file("database/bounding_boxes/Bounding_Boxes.json")
    word_boxes = load_json_file("database/bounding_boxes/Word_Bounding_Boxes.json")
    line_boxes = load_json_file("database/bounding_boxes/Line_Bounding_Boxes.json")
    
    # Get all unique marker IDs
    all_markers = set()
    for data in [paragraph_boxes, word_boxes, line_boxes]:
        for img in data["images"]:
            all_markers.add(img["id"])
    
    # Sort markers for consistent order
    all_markers = sorted(all_markers)
    
    # Process each marker
    for marker_id in all_markers:
        print(f"\nProcessing marker {marker_id}")
        
        # Load image
        img = load_image_for_marker(marker_id)
        if img is None:
            print(f"No image found for marker {marker_id}")
            continue
        
        # Find data for this marker
        paragraph_data = next((img for img in paragraph_boxes["images"] if img["id"] == marker_id), None)
        word_data = next((img for img in word_boxes["images"] if img["id"] == marker_id), None)
        line_data = next((img for img in line_boxes["images"] if img["id"] == marker_id), None)
        
        # Create a window
        window_name = f"Marker {marker_id} - Bounding Boxes"
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(window_name, 1200, 800)
        
        # Show original image
        cv2.imshow(window_name, img)
        print("Showing original image. Press any key to continue...")
        key = cv2.waitKey(0)
        if key == ord('q'):
            cv2.destroyAllWindows()
            return
        
        # Show paragraph boxes
        if paragraph_data:
            img_paragraphs = img.copy()
            for i, box in enumerate(paragraph_data["bounding_boxes"]):
                cv2.rectangle(img_paragraphs, 
                             (box["top_left"]["x"], box["top_left"]["y"]),
                             (box["bottom_right"]["x"], box["bottom_right"]["y"]),
                             (255, 0, 0), 2)  # Blue
                cv2.putText(img_paragraphs, f"P{i}", 
                           (box["top_left"]["x"], box["top_left"]["y"] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 0, 0), 2)
            
            cv2.imshow(window_name, img_paragraphs)
            print("Showing paragraph boxes (blue). Press any key to continue...")
            key = cv2.waitKey(0)
            if key == ord('q'):
                cv2.destroyAllWindows()
                return
        
        # Show word boxes
        if word_data:
            img_words = img.copy()
            for i, word in enumerate(word_data["word_boxes"]):
                cv2.rectangle(img_words, 
                             (word["top_left"]["x"], word["top_left"]["y"]),
                             (word["bottom_right"]["x"], word["bottom_right"]["y"]),
                             (0, 255, 0), 1)  # Green
            
            cv2.imshow(window_name, img_words)
            print("Showing word boxes (green). Press any key to continue...")
            key = cv2.waitKey(0)
            if key == ord('q'):
                cv2.destroyAllWindows()
                return
        
        # Show line boxes
        if line_data:
            img_lines = img.copy()
            for i, line in enumerate(line_data["line_boxes"]):
                cv2.rectangle(img_lines, 
                             (line["top_left"]["x"], line["top_left"]["y"]),
                             (line["bottom_right"]["x"], line["bottom_right"]["y"]),
                             (0, 0, 255), 1)  # Red
                cv2.putText(img_lines, f"L{i}", 
                           (line["top_left"]["x"] - 30, line["top_left"]["y"] + 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            cv2.imshow(window_name, img_lines)
            print("Showing line boxes (red). Press any key to continue...")
            key = cv2.waitKey(0)
            if key == ord('q'):
                cv2.destroyAllWindows()
                return
        
        # Show all boxes together
        img_all = img.copy()
        
        # Draw paragraph boxes
        if paragraph_data:
            for i, box in enumerate(paragraph_data["bounding_boxes"]):
                cv2.rectangle(img_all, 
                             (box["top_left"]["x"], box["top_left"]["y"]),
                             (box["bottom_right"]["x"], box["bottom_right"]["y"]),
                             (255, 0, 0), 2)  # Blue
                cv2.putText(img_all, f"P{i}", 
                           (box["top_left"]["x"], box["top_left"]["y"] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.9, (255, 0, 0), 2)
        
        # Draw word boxes
        if word_data:
            for word in word_data["word_boxes"]:
                cv2.rectangle(img_all, 
                             (word["top_left"]["x"], word["top_left"]["y"]),
                             (word["bottom_right"]["x"], word["bottom_right"]["y"]),
                             (0, 255, 0), 1)  # Green
        
        # Draw line boxes
        if line_data:
            for i, line in enumerate(line_data["line_boxes"]):
                cv2.rectangle(img_all, 
                             (line["top_left"]["x"], line["top_left"]["y"]),
                             (line["bottom_right"]["x"], line["bottom_right"]["y"]),
                             (0, 0, 255), 1)  # Red
                cv2.putText(img_all, f"L{i}", 
                           (line["top_left"]["x"] - 30, line["top_left"]["y"] + 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        cv2.imshow(window_name, img_all)
        print("Showing all boxes together. Press any key to continue to next marker...")
        key = cv2.waitKey(0)
        if key == ord('q'):
            cv2.destroyAllWindows()
            return
        
        cv2.destroyWindow(window_name)
    
    print("All markers processed.")

if __name__ == "__main__":
    main()