import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.signal import savgol_filter
from tools.proc import (
    apply_wiener_filter,
    calculate_velocity,
    load_image_for_marker,
    fixation_detection_velocity,
    pchip_interpolate,
    outlier_removal_in_col
)

def compare_filtering_methods(filepath, marker=113, window_size=7):
    """
    Compare different filtering methods on eye tracking data.
    
    Args:
        filepath (str): Path to the eye tracking CSV file
        marker (int): Marker to filter data for
        window_size (int): Window size for filters
    """
    # Read data
    df = pd.read_csv(filepath, sep=";")
    df = df[df['Marker'] == marker]
    
    if len(df) == 0:
        print(f"No data found for marker {marker}")
        return
    
    # Get necessary data
    x = df["Screen_X_filtered"].to_numpy()
    y = df["Screen_Y_filtered"].to_numpy()
    t = df["Timestamp"].to_numpy()
    pupil_left = df["Diameter_left"].to_numpy() * 2  # Convert radius to diameter
    pupil_right = df["Diameter_right"].to_numpy() * 2
    
    # Convert to relative time
    t_rel = (t - t[0])
    
    # Apply interpolation
    # Create DataFrames for interpolation
    x_df = pd.DataFrame({'Coordinate': x})
    y_df = pd.DataFrame({'Coordinate': y})
    pl_df = pd.DataFrame({'Diameter': pupil_left})
    pr_df = pd.DataFrame({'Diameter': pupil_right})
    
    # Find sequences of NaN values for interpolation
    for df_to_interp, col_name in [(x_df, 'Coordinate'), (y_df, 'Coordinate'), 
                                   (pl_df, 'Diameter'), (pr_df, 'Diameter')]:
        is_nan = np.isnan(df_to_interp[col_name]).to_numpy()
        if np.any(is_nan):
            nan_starts = np.where(np.diff(is_nan.astype(int)) == 1)[0] + 1
            nan_ends = np.where(np.diff(is_nan.astype(int)) == -1)[0] + 1
            
            # Handle case where data starts with NaN
            if is_nan[0]:
                nan_starts = np.insert(nan_starts, 0, 0)
                
            # Handle case where data ends with NaN
            if is_nan[-1]:
                nan_ends = np.append(nan_ends, len(is_nan))
            
            # Apply interpolation for each NaN sequence
            for start, end in zip(nan_starts, nan_ends):
                df_to_interp = pchip_interpolate(df_to_interp, col_name, start, end, 13, 300)
    
    # Extract interpolated data
    x_interp = x_df['Coordinate'].to_numpy()
    y_interp = y_df['Coordinate'].to_numpy()
    pupil_left_interp = pl_df['Diameter'].to_numpy()
    pupil_right_interp = pr_df['Diameter'].to_numpy()
    
    # Apply Wiener filter to original data
    x_wiener = apply_wiener_filter(x, window_size)
    y_wiener = apply_wiener_filter(y, window_size)
    pupil_left_wiener = apply_wiener_filter(pupil_left, window_size)
    pupil_right_wiener = apply_wiener_filter(pupil_right, window_size)
    
    # Calculate velocities
    velocity = calculate_velocity(x, y, t)
    velocity_interp = calculate_velocity(x_interp, y_interp, t)
    velocity_wiener = calculate_velocity(x_wiener, y_wiener, t)
    
    # Detect fixations
    fixations_original = fixation_detection_velocity(velocity, t, 500)
    fixations_interp = fixation_detection_velocity(velocity_interp, t, 500)
    fixations_wiener = fixation_detection_velocity(velocity_wiener, t, 500)
    
    # Create binary arrays for fixation periods
    fix_original = np.zeros_like(t_rel)
    fix_interp = np.zeros_like(t_rel)
    fix_wiener = np.zeros_like(t_rel)
    
    for start, end in fixations_original:
        fix_original[start:end] = 1
    
    for start, end in fixations_interp:
        fix_interp[start:end] = 1
        
    for start, end in fixations_wiener:
        fix_wiener[start:end] = 1
    
    # Create figure with subplots
    fig, axes = plt.subplots(3, 1, figsize=(15, 15), sharex=True)
    
    # Plot X coordinates
    axes[0].plot(t_rel, x, 'gray', alpha=0.5, label='Original')
    axes[0].plot(t_rel, x_interp, 'b-', label='Interpolated')
    axes[0].plot(t_rel, x_wiener, 'r-', label='Wiener Filter')
    
    # Add fixation blocks for X plot
    y_min, y_max = axes[0].get_ylim()
    
    axes[0].fill_between(t_rel, y_min, y_max, where=fix_original==1, 
                        color='gray', alpha=0.1, label='Original Fixations')
    axes[0].fill_between(t_rel, y_min, y_max, where=fix_interp==1, 
                        color='blue', alpha=0.1, label='Interpolated Fixations')
    axes[0].fill_between(t_rel, y_min, y_max, where=fix_wiener==1, 
                        color='red', alpha=0.1, label='Wiener Fixations')
    
    axes[0].set_ylabel('X Coordinate (pixels)')
    axes[0].set_title(f'Comparison of Filtering Methods - Marker {marker}')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Plot Y coordinates
    axes[1].plot(t_rel, y, 'gray', alpha=0.5, label='Original')
    axes[1].plot(t_rel, y_interp, 'b-', label='Interpolated')
    axes[1].plot(t_rel, y_wiener, 'r-', label='Wiener Filter')
    
    # Add fixation blocks for Y plot
    y_min, y_max = axes[1].get_ylim()
    
    axes[1].fill_between(t_rel, y_min, y_max, where=fix_original==1, 
                        color='gray', alpha=0.1, label='Original Fixations')
    axes[1].fill_between(t_rel, y_min, y_max, where=fix_interp==1, 
                        color='blue', alpha=0.1, label='Interpolated Fixations')
    axes[1].fill_between(t_rel, y_min, y_max, where=fix_wiener==1, 
                        color='red', alpha=0.1, label='Wiener Fixations')
    
    axes[1].set_ylabel('Y Coordinate (pixels)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # Plot pupil diameter (average of left and right)
    axes[2].plot(t_rel, (pupil_left + pupil_right)/2, 'gray', alpha=0.5, label='Original')
    axes[2].plot(t_rel, (pupil_left_interp + pupil_right_interp)/2, 'b-', label='Interpolated')
    axes[2].plot(t_rel, (pupil_left_wiener + pupil_right_wiener)/2, 'r-', label='Wiener Filter')
    
    # Add fixation blocks for pupil plot
    y_min, y_max = axes[2].get_ylim()
    
    axes[2].fill_between(t_rel, y_min, y_max, where=fix_original==1, 
                        color='gray', alpha=0.1, label='Original Fixations')
    axes[2].fill_between(t_rel, y_min, y_max, where=fix_interp==1, 
                        color='blue', alpha=0.1, label='Interpolated Fixations')
    axes[2].fill_between(t_rel, y_min, y_max, where=fix_wiener==1, 
                        color='red', alpha=0.1, label='Wiener Fixations')
    
    axes[2].set_ylabel('Pupil Diameter (mm)')
    axes[2].set_xlabel('Time (seconds)')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Create a second figure to compare gaze paths
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # Load image for background
    image = load_image_for_marker(marker)
    if image is not None:
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        for ax in axes:
            ax.imshow(image)
    
    # Plot original gaze path
    axes[0].plot(x, y, 'gray', alpha=0.5, linewidth=0.5)
    axes[0].set_title(f'Original Gaze Path ({len(fixations_original)} fixations)')
    
    # Plot interpolated gaze path
    axes[1].plot(x_interp, y_interp, 'b-', alpha=0.7, linewidth=0.5)
    axes[1].set_title(f'Interpolated Gaze Path ({len(fixations_interp)} fixations)')
    
    # Plot Wiener filtered gaze path
    axes[2].plot(x_wiener, y_wiener, 'r-', alpha=0.7, linewidth=0.5)
    axes[2].set_title(f'Wiener Filtered Gaze Path ({len(fixations_wiener)} fixations)')
    
    # Plot fixations
    for i, (start, end) in enumerate(fixations_original):
        fix_x = np.nanmean(x[start:end])
        fix_y = np.nanmean(y[start:end])
        axes[0].plot(fix_x, fix_y, 'bo', alpha=0.5, markersize=8)
    
    for i, (start, end) in enumerate(fixations_interp):
        fix_x = np.nanmean(x_interp[start:end])
        fix_y = np.nanmean(y_interp[start:end])
        axes[1].plot(fix_x, fix_y, 'bo', alpha=0.5, markersize=8)
    
    for i, (start, end) in enumerate(fixations_wiener):
        fix_x = np.nanmean(x_wiener[start:end])
        fix_y = np.nanmean(y_wiener[start:end])
        axes[2].plot(fix_x, fix_y, 'bo', alpha=0.5, markersize=8)
    
    # Print statistics
    print(f"Number of fixations (Original): {len(fixations_original)}")
    print(f"Number of fixations (Interpolated): {len(fixations_interp)}")
    print(f"Number of fixations (Wiener): {len(fixations_wiener)}")
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    filepath = "database/raw_eyetracking/subj-01_group-1_role-Operator_eye_tracking_20241210094555.csv"
    
    # Compare filtering methods for different markers
    for marker in [113, 121, 132]:
        compare_filtering_methods(filepath, marker=marker, window_size=7)
        