import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import cv2
from tools.proc import fixation_detection_velocity, fixation_detection_dispersion, load_bounding_boxes

def load_image_for_marker(marker):
    """Load the corresponding image for a given marker."""
    scenario = {"1": 'a', "2": 'b', "3": 'c', "4": 'd', "5": 'e', "6": 'f'}
    ti_type = {"1": "system_goal", "2": "system_type", "3": "data", "4": "control"}

    marker_str = str(marker).zfill(3)
    scenario_key, ti_key, page = marker_str[0], marker_str[1], marker_str[2]

    if scenario_key in scenario and ti_key in ti_type:
        image_filename = f"database/ti_png/{scenario[scenario_key]}_{ti_type[ti_key]}_{page}.png"
        return cv2.imread(image_filename)
    return None

def plot_gaze_and_fixations(filepath, marker=113):
    # Read data
    df = pd.read_csv(filepath, sep=";")
    df = df[df['Marker'] == marker]

    x = df["Screen_X_filtered"].to_numpy()
    y = df["Screen_Y_filtered"].to_numpy()
    t = df["Timestamp"].to_numpy()
    
    # Get fixations using both methods
    fixations_vel = fixation_detection_velocity(x, y, t, v_threshold=2000)
    fixations_disp = fixation_detection_dispersion(x, y, t, d_threshold=200)
    
    # Load image and bounding boxes
    image = load_image_for_marker(marker)
    if image is None:
        print(f"No image found for marker {marker}")
        return
        
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    bounding_boxes = load_bounding_boxes()
    marker_boxes = [img['bounding_boxes'] for img in bounding_boxes['images'] if img['id'] == marker]
    if marker_boxes:
        boxes = marker_boxes[0]
    else:
        boxes = []

    # Create figure with two subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # Plot 1: Velocity-based fixations
    ax1.imshow(image)
    ax1.plot(x, y, 'gray', alpha=0.3, linewidth=0.5, label='Gaze path')
    
    # Draw bounding boxes
    for i, box in enumerate(boxes):
        x1 = box['top_left']['x']
        y1 = box['top_left']['y']
        x2 = box['bottom_right']['x']
        y2 = box['bottom_right']['y']
        rect = plt.Rectangle((x1, y1), x2-x1, y2-y1, fill=False, color='green', alpha=0.5)
        ax1.add_patch(rect)
        ax1.text(x1, y1, str(i), color='green')
    
    # Plot velocity fixations
    for i, (start, end) in enumerate(fixations_vel):
        fix_x = np.mean(x[start:end])
        fix_y = np.mean(y[start:end])
        ax1.plot(fix_x, fix_y, 'ro', alpha=0.5, markersize=10)
        ax1.text(fix_x+10, fix_y+10, str(i), color='red', fontsize=8)
    
    ax1.set_title('Velocity-based Fixations')
    
    # Plot 2: Dispersion-based fixations
    ax2.imshow(image)
    ax2.plot(x, y, 'gray', alpha=0.3, linewidth=0.5, label='Gaze path')
    
    # Draw bounding boxes
    for i, box in enumerate(boxes):
        x1 = box['top_left']['x']
        y1 = box['top_left']['y']
        x2 = box['bottom_right']['x']
        y2 = box['bottom_right']['y']
        rect = plt.Rectangle((x1, y1), x2-x1, y2-y1, fill=False, color='green', alpha=0.5)
        ax2.add_patch(rect)
        ax2.text(x1, y1, str(i), color='green')
    
    # Plot dispersion fixations
    for i, (start, end) in enumerate(fixations_disp):
        fix_x = np.mean(x[start:end])
        fix_y = np.mean(y[start:end])
        ax2.plot(fix_x, fix_y, 'bo', alpha=0.5, markersize=10)
        ax2.text(fix_x+10, fix_y+10, str(i), color='blue', fontsize=8)
    
    ax2.set_title('Dispersion-based Fixations')
    
    # Print statistics
    print(f"Number of velocity-based fixations: {len(fixations_vel)}")
    print(f"Number of dispersion-based fixations: {len(fixations_disp)}")
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    filepath = "database/raw_eyetracking/subj-01_group-1_role-Operator_eye_tracking_20241210094555.csv"
    plot_gaze_and_fixations(filepath, marker=113)
