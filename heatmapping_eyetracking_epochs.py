import os
import numpy as np
import pandas as pd
import cv2
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter
import re
from tools.proc import fixation_detection_dispersion

base_dir= "database/raw_eyetracking"
def load_image_for_marker(marker):
    """
    Lädt das passende Bild für einen gegebenen Markerwert basierend auf den Codes.
    """
    scenario = {"1": 'a', "2": 'b', "3": 'c', "4": 'd', "5": 'e', "6": 'f'}
    ti_type = {"1": "system_goal", "2": "system_type", "3": "data", "4": "control"}

    marker_str = str(marker).zfill(3)  # Sicherstellen, dass der Marker dreistellig ist
    scenario_key, ti_key, page = marker_str[0], marker_str[1], marker_str[2]

    if scenario_key in scenario and ti_key in ti_type:
        image_filename = f"database/ti_png/{scenario[scenario_key]}_{ti_type[ti_key]}_{page}.png"
        if os.path.exists(image_filename):
            return cv2.imread(image_filename)
    return None


def generate_heatmap(x, y, width, height, bin_size=3, sigma=20):
    """
    Erstellt eine Heatmap basierend auf den Blickdaten mit einer Reds-Colormap
    """
    y_shape = height // bin_size
    x_shape = width // bin_size
    heatmap = np.zeros((y_shape, x_shape))

    for i in range(len(x)):
        if np.isnan(x[i]) or np.isnan(y[i]):  # NaN-Werte überspringen
            continue
        x_idx = min(max(int(x[i] // bin_size), 0), x_shape - 1)
        y_idx = min(max(int(y[i] // bin_size), 0), y_shape - 1)
        heatmap[y_idx, x_idx] += 1

    heatmap = gaussian_filter(heatmap, sigma=sigma)
    if np.max(heatmap) > 0:
        heatmap = heatmap / np.max(heatmap)
    heatmap = cv2.resize(heatmap, (width, height))
    return (heatmap * 255).astype(np.uint8)


def extract_epochs(markers):
    """
    Extrahiert zusammenhängende Epochen basierend auf den dreistelligen Markern
    """
    epochs = []
    start_idx = None
    current_marker = None

    for i, marker in enumerate(markers):
        marker_str = str(marker).zfill(3)
        if marker_str.isdigit() and len(marker_str) == 3:
            if start_idx is None:
                start_idx = i
                current_marker = marker_str
            elif marker_str != current_marker:
                epochs.append((start_idx, i - 1, current_marker))
                start_idx = i
                current_marker = marker_str

    if start_idx is not None:
        epochs.append((start_idx, len(markers) - 1, current_marker))

    return epochs

for d_path in os.listdir(base_dir):
    df = pd.read_csv(f"{base_dir}/{d_path}", sep=";")
    # Extrahiere die ID aus dem Dateipfad
    file_id = re.search(r'(?P<id>.*)_eye_tracking', d_path)
    file_id=file_id.group("id")
    print(file_id)
    subject_id=re.search(r'(subj-\d+)_', d_path)
    subject_id=subject_id.group(1)
    print(subject_id)

    output_dir=f"Heatmaps/{file_id}"
    os.makedirs(output_dir,exist_ok=True)

    x = df["Screen_X_filtered"].to_numpy()
    y = df["Screen_Y_filtered"].to_numpy()
    timestamp = df["Timestamp"].to_numpy()
    markers = df["Marker"].to_numpy()

    print("Dataframe loaded")
    epochs = extract_epochs(markers)

    for start, end, marker in epochs:
        image = load_image_for_marker(marker)
        if image is None:
            continue

        x_epoch = x[start:end + 1]
        y_epoch = y[start:end + 1]

        heatmap = generate_heatmap(x_epoch, y_epoch, image.shape[1], image.shape[0])
        heatmap_colored = cv2.applyColorMap(heatmap, cv2.COLORMAP_JET)  # Reds-Colormap
        blended = cv2.addWeighted(image, 0.7, heatmap_colored, 0.3, 0)

        output_path = f"{output_dir}/{subject_id}_{marker}.png"
        plt.imsave(output_path, cv2.cvtColor(blended, cv2.COLOR_BGR2RGB))
        plt.figure(figsize=(10, 6))
        #plt.imshow(cv2.cvtColor(blended, cv2.COLOR_BGR2RGB))
        plt.axis("off")
        plt.title(f"Heatmap für Marker {marker} ({subject_id})")
        plt.close()
        #plt.show()