import pandas as pd
import numpy as np
import os
import glob


class CFG:
    data_path = "database/raw_eyetracking"


def check_frame_number(data):
    frames = data.to_numpy()
    if len(frames) > 0:
        frames = frames.astype(int)

        diffs = np.diff(frames)
        frame_losses = np.argwhere(diffs > 1).shape[0]
        double_frames = np.argwhere(diffs == 0).shape[0]
        total_frames = frames[-1] - frames[0]
        total_length = total_frames / 250
        print(
            f"Loss Frames: {frame_losses}/{total_frames} ({(frame_losses / total_frames) * 100:.3f} %), Total length in min {total_length / 60:.2f} min")
        print(f"Double Frames: {double_frames}")
    else:
        print("No Frame data")


def check_nan_vals(data):
    x_nan_count = data["Screen_X_raw"].isna().sum()
    y_nan_count = data["Screen_Y_raw"].isna().sum()
    total = data.size
    print(f"X NaN Counts: {x_nan_count} ({x_nan_count / total * 100:.3f} %)")
    print(f"Y NaN Counts: {y_nan_count} ({y_nan_count / total * 100:.3f} %)")

def check_marker(data):
    uniques = np.unique(data)
    missing_markers = []
    for scenario in ["1", "2", "4", "5"]:
        for ti_type in ["1", "2", "3", "4"]:
            for page in ["1", "2", "3"]:
                marker = int(scenario + ti_type + page)
                if marker not in uniques:
                    missing_markers.append(marker)
                    # print(f"{marker} doesn't exist.")
    print(f"Missing Marker: {len(missing_markers)}")
    return missing_markers


def check_questionnaires(data):
    print(f"Number of questions: {len(data)}")


def main():
    missing_markers_data = []
    
    for data_path in os.listdir(CFG.data_path):
        d_path = os.path.join(CFG.data_path, data_path)
        print(f"-----------------")
        print(f"SUBJ: {d_path}")
        eye_data_path = glob.glob(f"{d_path}")[0]
        
        subject_id = os.path.basename(d_path).split('_')[0]
        
        try:
            df = pd.read_csv(eye_data_path, sep=";")
            print(f"Total time: {(df['Timestamp'].iloc[-1] - df['Timestamp'].iloc[0]) / 60:.2f} min")
            df_filtered = df[df["Marker"].astype(str).str.match(r'^\d{3}$')]

            check_frame_number(df_filtered["FrameNumber"])
            check_nan_vals(df_filtered)
            missing = check_marker(df_filtered["Marker"])
            
            # Add to our data collection
            for marker in missing:
                missing_markers_data.append({
                    'subject': subject_id,
                    'file': data_path,
                    'missing_marker': marker
                })
                
        except pd.errors.EmptyDataError:
            print("File is empty")
            # For empty files, add a row with marker -1
            missing_markers_data.append({
                'subject': subject_id,
                'file': data_path,
                'missing_marker': -1
            })

    # Create and save the missing markers DataFrame
    missing_df = pd.DataFrame(missing_markers_data)
    missing_df.to_csv("missing_markers.csv", index=False)
    print(f"\nMissing markers data saved to missing_markers.csv")

    # for data_path in os.listdir(CFG.quest_path):
    #     d_path = os.path.join(CFG.quest_path, data_path)
    #     print(f"SUBJ: {d_path}")
    #     df = pd.read_csv(d_path, sep=";", encoding="iso-8859-1")
    #     check_questionnaires(df)


if __name__ == '__main__':
    main()
