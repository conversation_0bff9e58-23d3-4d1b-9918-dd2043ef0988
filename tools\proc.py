import numpy as np
import matplotlib.pyplot as plt
import os
import cv2
import pandas as pd
from scipy.spatial import ConvexHull
import scipy
import json
from helper_eyetracking import outlier_removal_in_col, pchip_interpolate
import re
from scipy.signal import wiener

def safe_eval_array(arr_str):
    # Replace multiple spaces or newlines with a single space, then format it for eval
    cleaned_str = re.sub(r'\s+', ',', arr_str).replace('[,', '[')  
    return np.array(eval(cleaned_str))

def load_image_for_marker(marker):
    """
    Lädt das passende Bild für einen gegebenen Markerwert basierend auf den Codes.
    """
    scenario = {"1": 'a', "2": 'b', "3": 'c', "4": 'd', "5": 'e', "6": 'f'}
    ti_type = {"1": "system_goal", "2": "system_type", "3": "data", "4": "control"}

    marker_str = str(marker).zfill(3)  # <PERSON><PERSON><PERSON><PERSON>, dass der Marker dreistellig ist
    scenario_key, ti_key, page = marker_str[0], marker_str[1], marker_str[2]

    if scenario_key in scenario and ti_key in ti_type:
        image_filename = f"database/ti_png/{scenario[scenario_key]}_{ti_type[ti_key]}_{page}.png"
        if os.path.exists(image_filename):
            return cv2.imread(image_filename)
    return cv2.imread(f"database/ti_png/default.png")

def resave_images_without_icc():
    """Resave all images in the database/ti_png directory without ICC profile."""
    for filename in os.listdir("database/ti_png"):
        if filename.endswith(".png"):
            img = cv2.imread(os.path.join("database/ti_png", filename))
            cv2.imwrite(os.path.join("database/ti_png", filename), img)

def load_bounding_boxes():
    with open("database/bounding_boxes/Bounding_Boxes.json", "r") as f:
        return json.load(f)
def load_lines():
    with open("database/bounding_boxes/Line_Bounding_Boxes.json", "r") as f:
        return json.load(f)
def load_words():
    with open("database/bounding_boxes/Word_Bounding_Boxes.json", "r") as f:
        return json.load(f)
    
def calculate_velocity(x, y, t):
    """Calculate velocity by dividing the Euclidean distance between two samples by the time difference.

    Args:
        x (numpy.ndarray): x coordinates
        y (numpy.ndarray): y coordinates
        t (numpy.ndarray): timestamp

    Returns:
        numpy.ndarray: Array of velocities in pixel per second
    """
    v = np.sqrt(np.diff(x, prepend=0) ** 2 + np.diff(y, prepend=0) ** 2) / np.diff(t, prepend=0)
    return v


def fixation_detection_velocity(velocities, t, v_threshold=10, duration_threshold=0.1):
    """Calculate fixations based on velocity threshold (I-VT).

    Args:
        velocities (numpy.ndarray): List of velocities
        t (numpy.ndarray): timestamp
        v_threshold (float, optional): velocity threshold pixel per second (everything under this threshold is a fixation). Defaults to 10.
        duration_threshold (float, optional): minimal duration to consider as fixation in seconds. Defaults to 0.1s.

    Returns:
        numpy.ndarray: Array of fixation indices (start_idx, end_idx), corresponding to x,y samples
    """
    fixations = []
    start_idx = None

    for i, v in enumerate(velocities):
        if v < v_threshold:
            if start_idx is None:
                start_idx = i
        else:
            if start_idx is not None:
                end_idx = i
                if t[end_idx] - t[start_idx] >= duration_threshold:
                    fixations.append((start_idx, end_idx))
                start_idx = None

    return np.array(fixations)


def calculate_dispersion(x, y):
    """Calculate the dispersion measure for a given group of points.

    Args:
        x (numpy.ndarray): x coordinates
        y (numpy.ndarray): y coordinates

    Returns:
        float: Dispersion measure = (max_x - min_x) + (max_y - min_y)
    """
    return (np.max(x) - np.min(x)) + (np.max(y) - np.min(y))


def fixation_detection_dispersion(x, y, t, d_threshold=200, duration_threshold=0.1):
    """Optimized fixation detection using efficient moving window approach.

    Args:
        x (numpy.ndarray): x coordinates of gaze data
        y (numpy.ndarray): y coordinates of gaze data
        t (numpy.ndarray): timestamps of gaze data
        d_threshold (float, optional): maximum dispersion within a window to be considered a fixation. Defaults to 200.
        duration_threshold (float, optional): minimum duration (in seconds) required for a fixation. Defaults to 0.1.

    Returns:
        numpy.ndarray: List of fixations as (start_idx, end_idx)
    """
    fixations = []
    start_idx = 0
    n = len(x)

    while start_idx < n:
        end_idx = start_idx + 1
        
        # Expand window as long as dispersion is within threshold
        while end_idx < n and calculate_dispersion(x[start_idx:end_idx], y[start_idx:end_idx]) <= d_threshold:
            end_idx += 1

        # Check if the fixation meets the duration threshold
        if t[end_idx - 1] - t[start_idx] >= duration_threshold:
            fixations.append((start_idx, end_idx - 1))
            # Jump to the end of this fixation
            start_idx = end_idx - 1
        
        start_idx += 1

    return np.array(fixations)


def get_fixations_for_markers(markers, fixations):
    """Calculate the fixations per marker.

    Args:
        markers (numpy.ndarray): Markers of gaze data
        fixations (numpy.ndarray): List of fixations as (start_idx, end_idx)

    Returns:
        dict: Dictionary with marker as key and list of fixations as value
    """

    fixations_per_marker = {}
    for m in np.unique(markers):
        if m < 100:
            continue
        markermask_idx = np.where(markers == m)[0]
        marker_start_idx = markermask_idx[0]
        marker_end_idx = markermask_idx[-1]
        fixations_per_marker[m] = fixations[(fixations[:,0] >= marker_start_idx) & (fixations[:,1] <= marker_end_idx)]
    return fixations_per_marker


def fixation_length_per_marker(markers, fixations, t):
    """Calculate the length of fixations per marker.

    Args:
        markers (numpy.ndarray): Markers of gaze data
        fixations (numpy.ndarray): List of fixations as (start_idx, end_idx)
        t (numpy.ndarray): Timestamps of gaze data

    Returns:
        dict: Dictionary with marker as key and fixation lengths in this marker as value
    """
    
    fixations_per_marker = get_fixations_for_markers(markers, fixations)

    fixation_lengths = {}
    for m, fixations in fixations_per_marker.items():
        fixation_lengths[m] = [t[end] - t[start] for start, end in fixations]
  
    return fixation_lengths


def saccade_detection_velocity(velocities, v_threshold=10):
    """Calculate saccades based on velocity threshold (I-VT).

    Args:
        velocities (numpy.ndarray): List of velocities
        v_threshold (float, optional): velocity threshold pixel per second (everything above this threshold is a saccade). Defaults to 10.

    Returns:
        numpy.ndarray: Array of saccade indices (start_idx, end_idx), corresponding to x,y samples
    """
    saccades = []
    start_idx = None
    for i, v in enumerate(velocities):
        if v > v_threshold:
            if start_idx is None:
                start_idx = i
        else:
            if start_idx is not None:
                saccades.append((start_idx, i - 1))
                start_idx = None
                
    return np.array(saccades)


# fixation count per area and marker
def fixation_count_per_area_and_marker(x, y, markers, fixations):
    """Calculate the number of fixations per area and marker.

    Args:
        x (numpy.ndarray): x coordinates of gaze data
        y (numpy.ndarray): y coordinates of gaze data
        markers (numpy.ndarray): Markers of gaze data
        fixations (numpy.ndarray): List of fixations as (start_idx, end_idx)

    Returns:
        dict: Dictionary containing the number of fixations per area and marker
    """
    fixation_count = {}
    paragraphs = get_paragraph_from_coords(x, y, markers, fixations)
    for marker_paragraph  in paragraphs.values():
        if marker_paragraph not in fixation_count:
            fixation_count[marker_paragraph] = 0
        fixation_count[marker_paragraph] += 1
    return fixation_count


def get_paragraph_from_coords(x, y, markers, fixations):
    """Calculate the paragraphs in which the fixations are located.

    Args:
        x (numpy.ndarray): x coordinates of gaze data
        y (numpy.ndarray): y coordinates of gaze data
        markers (numpy.ndarray): Markers of gaze data
        fixations (numpy.ndarray): List of fixations as (start_idx, end_idx)

    Returns:
        dict: Dictionary with fixation start_idx as key and (marker, paragraph) as value if hit, or (marker,-1) otherwise
    """
    paragraphs = {}
    bounding_box_json = load_bounding_boxes()
    marker_images = bounding_box_json['images']
    fixations_per_marker = get_fixations_for_markers(markers, fixations)
    for m, fixations in fixations_per_marker.items():
        marker_image = [img for img in marker_images if img['id'] == m][0]
        bounding_boxes = marker_image['bounding_boxes']
        for fixation in fixations:
            start, end = fixation
            for j, bounding_box in enumerate(bounding_boxes):
                # if start and end point of the fixation are in the bounding box, count it in
                if ((x[start] > bounding_box['top_left']['x'] and x[start] < bounding_box['bottom_right']['x']
                    and y[start] > bounding_box['top_left']['y'] and y[start] < bounding_box['bottom_right']['y'])
                    and (x[end] > bounding_box['top_left']['x'] and x[end] < bounding_box['bottom_right']['x']
                    and y[end] > bounding_box['top_left']['y'] and y[end] < bounding_box['bottom_right']['y'])):

                    paragraphs[start] = (m, j)
                    break
                else:
                    paragraphs[start] = (m, -1)
                
    return paragraphs


# convex hull size per fixation
def get_hull_size(x, y, fixations):
    """Calculate the size of the convex hull for a given group of points.

    Args:
        x (numpy.ndarray): x coordinates of gaze data
        y (numpy.ndarray): y coordinates of gaze data
        fixations (numpy.ndarray): List of fixations as (start_idx, end_idx)

    Returns:
        numpy.ndarray: Array with the sizes of the convex hulls
    """
    hull_sizes = []
    for start, end in fixations:
        hull = ConvexHull(np.array(list(zip(x[start:end], y[start:end]))))
        hull_sizes.append(hull.volume)
    return np.array(hull_sizes)


def get_pupil_diameter_per_marker(marker, pupil_diameter_left, pupil_diameter_right):
    """Calculate the average pupil diameter per marker.

    Args:
        marker (numpy.ndarray): Markers of gaze data
        pupil_diameter_left (numpy.ndarray): Pupil diameter of left eyes
        pupil_diameter_right (numpy.ndarray): Pupil diameter of right eyes

    Returns:
        dict: Dictionary with marker as key and pupil diameters as value

    Notes
    -----
        The actual given values for pupil diameter are actually the radius,
        so we multiply by 2 here (by just adding the radii to get the average diameter)

    """
    # first preprocess the diameters, by interpolating blinks with pchip, 
    # as well as excluding outliers
    interpolate_arrays = [pupil_diameter_left, pupil_diameter_right]

    # plt.plot(pupil_diameter_left, color='blue', alpha=0.5)

    for i, pupil_diameter in enumerate((pupil_diameter_left, pupil_diameter_right)):
        # Find sequences of NaN values
        is_nan = np.isnan(pupil_diameter) 
        nan_starts = np.where(np.diff(is_nan.astype(int)) == 1)[0] + 1
        nan_ends = np.where(np.diff(is_nan.astype(int)) == -1)[0] + 1
        
        # Handle case where data starts or ends with NaN
        if is_nan[0]:
            nan_starts = np.insert(nan_starts, 0, 0)

        interpolate_df = pd.DataFrame()
        interpolate_df['Diameter'] = pupil_diameter

        for start, end in zip(nan_starts, nan_ends):
            interpolate_df = pchip_interpolate(interpolate_df, 'Diameter', start, end, 13, 175)

        interpolate_df = outlier_removal_in_col(interpolate_df, 'Diameter', 0.05, 0.95)
        
        interpolate_arrays[i] = interpolate_df['Diameter'].to_numpy()
        del interpolate_df

    pupil_diameter_left, pupil_diameter_right = interpolate_arrays

    # plt.plot(pupil_diameter_left, color='red', alpha=0.5)
    # plt.show()

    pupil_diameters = {}    
    for m in np.unique(marker[marker>100]):
        pupil_diameters[m] = (pupil_diameter_left[marker == m] 
                              + pupil_diameter_right[marker == m])
    return pupil_diameters

def get_positions_per_marker(marker, x, y):
    """Calculate the average positions per marker.
    
    Args:
        marker (numpy.ndarray): List of all markers
        x (numpy.ndarray): x coordinates of gaze data
        y (numpy.ndarray): y coordinates of gaze data

    Returns:
        dict: Dictionary with marker as key and average positions as value
    """
    positions = {}
    for m in np.unique(marker):
        if m < 100:
            continue
        positions[m] = np.array([x[marker == m], y[marker == m]])
    return positions
    
def get_fixation_positions_per_marker(marker, fixations, x, y):
    """Calculate the average fixation positions per marker.
    
    Args:
        marker (numpy.ndarray): List of all markers
        fixations (numpy.ndarray): Fixation data list of (start_idx, end_idx)
        x (numpy.ndarray): x coordinates of gaze data
        y (numpy.ndarray): y coordinates of gaze data

    Returns:
        dict: Dictionary with marker as key and list of fixation positions per fixation as value
    """
    fixation_positions = {}
    for m, marker_fixations in get_fixations_for_markers(marker, fixations).items():
        fixation_positions[m] = np.array([(np.mean(x[start:end]), np.mean(y[start:end])) for start, end in marker_fixations])
    
    return fixation_positions

def get_fixation_frequency_per_marker(marker, fixations, t):
    """Calculate the fixation frequency per marker.

    Args:
        marker (numpy.ndarray): List of all markers
        fixations (numpy.ndarray): Fixation data list of (start_idx, end_idx)
        t (numpy.ndarray): Timestamps of gaze data

    Returns:
        dict: Dictionary with marker as key and fixation frequency as value
    """

    fixations_per_marker = get_fixations_for_markers(marker, fixations)
    marker_lengths = get_marker_duration(marker, t)

    fixation_frequency = {}
    for m in np.unique(marker):
        if m < 100:
            continue
        fixation_frequency[m] = len(fixations_per_marker[m]) / marker_lengths[m]

    return fixation_frequency
##################################################################################################
##################################################################################################
##################################################################################################
def get_saccade_frequency_per_marker(marker, saccades, t):
    """Calculate the saccade frequency per marker.

    Args:
        marker (numpy.ndarray): List of all markers
        saccades (numpy.ndarray): Saccade data list of (start_idx, end_idx)
        t (numpy.ndarray): Timestamps of gaze data

    Returns:
        dict: Dictionary with marker as key and saccade frequency as value
    """
    saccade_per_markers = get_saccades_for_markers(saccades, marker)
    marker_durations = get_marker_duration(marker, t)
    saccade_frequency = {}

    for m in np.unique(marker):
        if m < 100:
            continue
        saccade_frequency[m] = len(saccade_per_markers[m]) / marker_durations[m]

    return saccade_frequency

def get_saccade_velocity_per_saccade_per_marker(velocities, marker, saccades):
    """Calculate the saccade velocity per saccade.
    
    Args:
        velocities (numpy.ndarray): List of velocities
        marker (numpy.ndarray): List of all markers
        saccades (numpy.ndarray): Saccade data list of (start_idx, end_idx)

    Returns:
        dict: Dictionary with marker as key and list of saccade velocities as value
    """
    saccade_per_markers = get_saccades_for_markers(saccades, marker)
    saccade_velocities = {}
    for m in np.unique(marker):
        if m < 100:
            continue
        saccade_velocities[m] = np.array([np.nanmean(velocities[start:end]) if len(velocities[start:end]) > 0 else np.nan for start, end in saccade_per_markers[m]])
        
    return saccade_velocities

def get_peak_velocity_per_saccade_per_marker(velocities, marker, saccades):
    """Calculate the peak velocity per saccade.
    
    Args:
        velocities (numpy.ndarray): List of velocities
        marker (numpy.ndarray): List of all markers
        saccades (numpy.ndarray): Saccade data list of (start_idx, end_idx)

    Returns:
        dict: Dictionary with marker as key and list of peak saccade velocities as value
    """
    saccade_per_markers = get_saccades_for_markers(saccades, marker)
    peak_saccade_velocity = {}
    for m in np.unique(marker):
        if m < 100:
            continue
        peak_saccade_velocity[m] = [np.nanmax(velocities[start:end]) for start, end in saccade_per_markers[m] if len(velocities[start:end]) > 0]
        
    return peak_saccade_velocity

def get_saccades_for_markers(saccades, marker):
    """filter the saccades per marker.
    
    Args:
        saccades (numpy.ndarray): Saccade data list of (start_idx, end_idx)
        marker (numpy.ndarray): List of all markers

    Returns:
        dict: Dictionary with marker as key and the saccades as value
    """
    saccades_per_marker = {}
    for m in np.unique(marker):
        if m < 100:
            continue
        markermask_idx = np.where(marker == m)[0]
        marker_start_idx = markermask_idx[0]
        marker_end_idx = markermask_idx[-1]
        saccades_per_marker[m] = saccades[(saccades[:,0] >= marker_start_idx) & (saccades[:,1] <= marker_end_idx)]

    return saccades_per_marker

def get_saccade_duration_per_saccade_per_marker(saccades, t, marker):
    """Calculate the saccade duration per saccade.
    
    Args:
        saccades (numpy.ndarray): Saccade data list of (start_idx, end_idx)
        t (numpy.ndarray): Timestamps of gaze data
        marker (numpy.ndarray): List of all markers

    Returns:
        dict: Dictionary with marker as key and list of saccade durations as value
    """
    saccade_per_markers = get_saccades_for_markers(saccades, marker)
    saccade_durations = {}
    for m, saccade in saccade_per_markers.items():
        saccade_durations[m] = [t[end] - t[start] for start, end in saccade]
    return saccade_durations


def get_peak_velocity_per_marker(velocities, marker):
    """Calculate the maximum (saccade) velocity per marker.

    Args:
        velocities (numpy.ndarray): List of velocities
        marker (numpy.ndarray): List of all markers

    Returns:
        dict: Dictionary with marker as key and maximum saccade velocity as value
    """
    peak_saccade_velocity = {}
    for m in np.unique(marker):
        if m < 100:
            continue

        marker_mask = marker == m
        peak_saccade_velocity[m] = np.nanmax(velocities[marker_mask])

    return peak_saccade_velocity


# backtracking to previous paragraphs.
def backtracking_to_previous_paragraphs(x, y, markers, fixations):
    """Calculate the number of returns to previous paragraphs after having already looked at later paragraphs.

    Args:
        x (numpy.ndarray): x coordinates of gaze data
        y (numpy.ndarray): y coordinates of gaze data
        markers (numpy.ndarray): Markers of gaze data
        fixations (numpy.ndarray): List of fixations as (start_idx, end_idx)

    Returns:
        dict: Dictionary with number of returns to previous paragraphs per marker
    """

    paragraphs = get_paragraph_from_coords(x, y, markers, fixations)
    backtracking_counts = {}
    for marker in markers:
        if marker < 100:
            continue
        backtracking_counts[marker] = 0
    previous_latest_paragraph = 0
    last_marker = 100

    for fix_start_idx in np.sort(list(paragraphs.keys())): # make sure we go in increasing order of timestamps
        marker, paragraph = paragraphs[fix_start_idx]

        if marker != last_marker:
            # on marker change, reset the paragraph counter
            last_marker = marker
            previous_latest_paragraph = 0
        if str(marker)[2] == "1":
            continue
        # mark if a previous paragraph is visited
        if paragraph < previous_latest_paragraph:
            backtracking_counts[marker] += 1
        else:
            previous_latest_paragraph = paragraph
    

    return backtracking_counts


def plot_velocities(velocities, t, v_threshold, output_path):
    """Plot velocities and threshold for visualization.
    
    Args:
        x (numpy.ndarray): x coordinates
        y (numpy.ndarray): y coordinates
        t (numpy.ndarray): timestamps
        v_threshold (float): velocity threshold used for fixation detection
        output_path (str): path where to save the plot
    """
  
    plt.figure(figsize=(15, 5))
    plt.plot(t, velocities, 'b-', label='Velocity', alpha=0.6)
    plt.axhline(y=v_threshold, color='r', linestyle='--', label=f'Threshold ({v_threshold})')
    
    plt.xlabel('Time (s)')
    plt.ylabel('Velocity (pixels/s)')
    plt.title('Eye Movement Velocities')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    # Save plot
    plt.savefig(output_path)
    plt.close()

def get_scanpath_lengths_per_marker(markers, fixations, x, y):
    """Calculate the scanpath length per marker.
    
    Args:
        markers (numpy.ndarray): Markers of gaze data
        fixations (numpy.ndarray): List of fixations as (start_idx, end_idx)
        x (numpy.ndarray): x coordinates
        y (numpy.ndarray): y coordinates

    Returns:
        dict: Dictionary with marker as key and scanpath length as value
    """
    scanpath_lengths = {}
    fixations_per_marker = get_fixations_for_markers(markers, fixations)
    for m in np.unique(markers):
        if m < 100:
            continue
        scanpath_lengths[m] = np.array(np.sqrt(np.diff(x[fixations_per_marker[m][:, 0]])**2 + np.diff(y[fixations_per_marker[m][:, 0]])**2))
    return scanpath_lengths

def get_blinks_per_marker(markers, x, y, t, max_blink_duration=0.4, min_blink_duration=0.1):
    """Calculate the blink frequency (blinks per second) per marker.
    A blink is defined as a sequence of NaN values in either x or y,
    not exceeding max_blink_duration seconds.

    Args:
        markers (numpy.ndarray): Markers of gaze data
        x (numpy.ndarray): x coordinates
        y (numpy.ndarray): y coordinates
        t (numpy.ndarray): Timestamps of gaze data
        max_blink_duration (float, optional): Maximum duration in seconds for a sequence 
            of NaN to be considered a blink. Defaults to 0.4.

    Returns:
        dict: Dictionary with marker as key and blink frequency (blinks/second) as value
    """
    blink_frequency = {}
    blink_counts = {}

    # Find sequences of NaN values
    is_nan = np.isnan(x) | np.isnan(y)
    nan_starts = np.where(np.diff(is_nan.astype(int)) == 1)[0] + 1
    nan_ends = np.where(np.diff(is_nan.astype(int)) == -1)[0] + 1
    
    # Handle case where data starts or ends with NaN
    if is_nan[0]:
        nan_starts = np.insert(nan_starts, 0, 0)

    for m in np.unique(markers):
        if m < 100:
            continue
        blink_counts[m] = 0

    # Process each NaN sequence
    for start, end in zip(nan_starts, nan_ends):
        duration = t[end] - t[start]
        
        # Only count as blink if duration is within reasonable range
        if duration <= max_blink_duration and duration >= min_blink_duration:
            # Get marker at start of blink
            marker = markers[start]
            if marker >= 100:
                blink_counts[marker] += 1
    marker_durations = get_marker_duration(markers, t)
    
    # Calculate frequencies
    for m, marker_duration in marker_durations.items():
        blink_frequency[m] = blink_counts[m] / marker_duration if marker_duration > 0 else 0
    
    return blink_frequency

def get_blink_frequency_rolling_window(x, y, t, markers, window_size=250*5, max_blink_duration=0.4, min_blink_duration=0.1):
    """Calculate blinks per second using a rolling window approach.
    gives a timeseries of blink frequency per second.
    A blink is defined as a sequence of NaN values in either x or y,
    not exceeding max_blink_duration seconds.

    Args:
        x (numpy.ndarray): x coordinates
        y (numpy.ndarray): y coordinates
        t (numpy.ndarray): Timestamps of gaze data
        markers (numpy.ndarray): Markers of gaze data
        window_size (int, optional): Size of the rolling window in samples. Defaults to 100.
        max_blink_duration (float, optional): Maximum duration in seconds for a sequence 
            of NaN to be considered a blink. Defaults to 0.4.

    Returns:
        numpy.ndarray: Array of blink frequencies per second

        """
    blink_counts = np.zeros_like(t)
    is_nan = np.isnan(x) | np.isnan(y)
    nan_starts = np.where(np.diff(is_nan.astype(int)) == 1)[0] + 1
    nan_ends = np.where(np.diff(is_nan.astype(int)) == -1)[0] + 1
    for start, end in zip(nan_starts, nan_ends):
        duration = t[end] - t[start]
        if duration <= max_blink_duration and duration >= min_blink_duration:
            blink_counts[start:end] += 1
    blink_counts = np.convolve(blink_counts, np.ones(window_size)/window_size, mode='same')

    plt.plot(t, blink_counts)
    # add markers as vertical lines to the plot
    for m in np.unique(markers):
        if m < 100:
            continue
        marker_mask = markers == m
        plt.axvline(x=t[marker_mask][0], color='r', linestyle='--', alpha=0.3)

    plt.show()

    return blink_counts
    



def get_marker_duration(markers, t):
    """Calculate the length of each marker.

    Args:
        markers (numpy.ndarray): Markers of gaze data
        t (numpy.ndarray): Timestamps of gaze data

    Returns:
        dict: Dictionary with marker as key and marker length in s as value
    """
    marker_lengths = {}
    for m in np.unique(markers):
        if m < 100:
            continue
        marker_mask = markers == m
        marker_lengths[m] = t[marker_mask][-1] - t[marker_mask][0]
    return marker_lengths

def filter_saccades_near_nan(saccades, x, y, buffer_samples=5):
    """Filter out saccades that are near NaN values in the data.
    
    Args:
        saccades (numpy.ndarray): Array of saccade indices (start_idx, end_idx)
        x (numpy.ndarray): x coordinates of gaze data
        y (numpy.ndarray): y coordinates of gaze data
        buffer_samples (int, optional): Number of samples to check before and after each saccade. Defaults to 5.
        
    Returns:
        numpy.ndarray: Filtered array of saccades with those near NaN values removed
    """
    filtered_saccades = []
    
    for start, end in saccades:
        # Define the region to check for NaNs (including buffer before and after)
        check_start = max(0, start - buffer_samples)
        check_end = min(len(x), end + buffer_samples)
        
        # Check if there are any NaN values in this region
        if not np.any(np.isnan(x[check_start:check_end])) and not np.any(np.isnan(y[check_start:check_end])):
            filtered_saccades.append((start, end))
    
    return np.array(filtered_saccades)

def saccade_detection_velocity_with_direction(x, y, velocities, v_threshold=1000, angle_threshold=45, filter_near_nan=True, nan_buffer=5):
    """Detect saccades using velocity threshold and split them at significant direction changes.

    Args:
        x (numpy.ndarray): x coordinates of gaze data
        y (numpy.ndarray): y coordinates of gaze data
        velocities (numpy.ndarray): Array of velocities
        v_threshold (float, optional): Velocity threshold for saccade detection. Defaults to 1000.
        angle_threshold (float, optional): Angle threshold in degrees for significant direction changes. Defaults to 45.
        filter_near_nan (bool, optional): Whether to filter out saccades near NaN values. Defaults to True.
        nan_buffer (int, optional): Number of samples to check before and after each saccade. Defaults to 5.

    Returns:
        numpy.ndarray: List of saccades as (start_idx, end_idx)
    """
    # First detect saccades using standard velocity threshold
    standard_saccades = saccade_detection_velocity(velocities, v_threshold=v_threshold)
    
    # Then split saccades at significant direction changes
    modified_saccades = []
    for start, end in standard_saccades:
        # Skip very short saccades
        if end - start < 3:
            modified_saccades.append((start, end))
            continue
            
        # Calculate direction changes
        dx = np.diff(x[start:end+1])
        dy = np.diff(y[start:end+1])
        angles = np.arctan2(dy, dx) * 180 / np.pi
        angle_changes = np.abs(np.diff(angles))
        # Adjust for circular nature of angles
        angle_changes = np.minimum(angle_changes, 360 - angle_changes)
        
        # Find significant direction changes
        significant_changes = np.where(angle_changes > angle_threshold)[0]
        
        if len(significant_changes) == 0:
            # No significant changes, keep original saccade
            modified_saccades.append((start, end))
        else:
            # Split saccade at direction changes
            split_points = [start] + [start + idx + 1 for idx in significant_changes] + [end]
            for i in range(len(split_points) - 1):
                modified_saccades.append((split_points[i], split_points[i+1]))
    
    # Filter out saccades near NaN values if requested
    if filter_near_nan:
        modified_saccades = filter_saccades_near_nan(np.array(modified_saccades), x, y, buffer_samples=nan_buffer)
    
    return np.array(modified_saccades)

def calculate_acceleration(velocities, t):
    """Calculate acceleration by taking the derivative of velocity.

    Args:
        velocities (numpy.ndarray): Array of velocities
        t (numpy.ndarray): timestamp

    Returns:
        numpy.ndarray: Array of accelerations
    """
    # Calculate time differences
    dt = np.diff(t, prepend=t[0])
    # Avoid division by zero
    dt[dt == 0] = np.finfo(float).eps
    
    # Calculate acceleration as change in velocity over time
    acceleration = np.diff(velocities, prepend=velocities[0]) / dt
    
    return acceleration

def saccade_detection_acceleration(x, y, t, velocities=None, v_threshold=1000, 
                                  acc_threshold=30000, deacc_threshold=-30000, 
                                  min_duration=0.01, max_duration=0.1):
    """Detect saccades using both velocity and acceleration thresholds.
    
    A saccade is characterized by:
    1. High velocity (above v_threshold)
    2. Initial high acceleration (above acc_threshold)
    3. Final high deceleration (below deacc_threshold)
    4. Duration within typical saccade range (min_duration to max_duration)

    Args:
        x (numpy.ndarray): x coordinates of gaze data
        y (numpy.ndarray): y coordinates of gaze data
        t (numpy.ndarray): timestamp
        velocities (numpy.ndarray, optional): Pre-calculated velocities. If None, will be calculated.
        v_threshold (float, optional): Velocity threshold for saccade detection. Defaults to 1000.
        acc_threshold (float, optional): Acceleration threshold for saccade onset. Defaults to 30000.
        deacc_threshold (float, optional): Deceleration threshold for saccade offset. Defaults to -30000.
        min_duration (float, optional): Minimum saccade duration in seconds. Defaults to 0.01.
        max_duration (float, optional): Maximum saccade duration in seconds. Defaults to 0.1.

    Returns:
        numpy.ndarray: Array of saccade indices (start_idx, end_idx)
    """
    # Calculate velocities if not provided
    if velocities is None:
        velocities = calculate_velocity(x, y, t)
    
    # Calculate acceleration
    acceleration = calculate_acceleration(velocities, t)
    
    # Find potential saccade onsets (high acceleration)
    potential_onsets = np.where(acceleration > acc_threshold)[0]
    
    # Find potential saccade offsets (high deceleration)
    potential_offsets = np.where(acceleration < deacc_threshold)[0]
    
    # Initialize saccades list
    saccades = []
    
    # Process each potential onset
    for onset_idx in potential_onsets:
        # Find the next potential offset after this onset
        offset_candidates = potential_offsets[potential_offsets > onset_idx]
        
        if len(offset_candidates) == 0:
            continue
            
        offset_idx = offset_candidates[0]
        
        # Check if the duration is within typical saccade range
        duration = t[offset_idx] - t[onset_idx]
        if duration < min_duration or duration > max_duration:
            continue
            
        # Check if velocity exceeds threshold during the saccade
        if np.max(velocities[onset_idx:offset_idx+1]) < v_threshold:
            continue
            
        # Valid saccade found
        saccades.append((onset_idx, offset_idx))
    
    # Convert to numpy array and return
    return np.array(saccades)

def filter_fixations_outside_paragraphs(fixation_XY, marker, margin=50):
    """Remove fixations that are outside paragraph boundaries plus a margin.
    
    Args:
        fixation_XY (numpy.ndarray): Array of fixation coordinates (x, y)
        marker (int): Marker ID to identify the correct image/paragraphs
        margin (int, optional): Extra margin around paragraphs in pixels. Defaults to 50.
        
    Returns:
        numpy.ndarray: Filtered fixation coordinates with only valid fixations
    """
    # Load bounding boxes for the marker
    bounding_box_json = load_bounding_boxes()
    marker_images = bounding_box_json['images']
    marker_image = [img for img in marker_images if img['id'] == marker]
    
    if not marker_image:
        return fixation_XY  # Return original if marker not found
    
    bounding_boxes = marker_image[0]['bounding_boxes']
    valid_indices = []
    
    # Check each fixation
    for i, (x, y) in enumerate(fixation_XY):
        is_valid = False
        for box in bounding_boxes:
            # Check if fixation is within any paragraph box (with margin)
            x1 = box['top_left']['x'] - margin
            y1 = box['top_left']['y'] - margin
            x2 = box['bottom_right']['x'] + margin
            y2 = box['bottom_right']['y'] + margin
            
            if x1 <= x <= x2 and y1 <= y <= y2:
                is_valid = True
                break
        
        if is_valid:
            valid_indices.append(i)
    
    # Return only valid fixations
    return fixation_XY[valid_indices]

def apply_wiener_filter(data, window_size=5):
    """Apply Wiener filter to data while preserving NaN values.
    
    Args:
        data (numpy.ndarray): Input data array
        window_size (int): Size of the filter window
        
    Returns:
        numpy.ndarray: Filtered data with same dimensions as input
    """
    # Create a mask for NaN values
    nan_mask = np.isnan(data)
    
    # Create a copy of the data with NaNs replaced by interpolated values
    filtered_data = np.copy(data)
    
    # Replace NaNs with interpolated values for filtering
    if np.any(nan_mask):
        valid_indices = np.where(~nan_mask)[0]
        if len(valid_indices) > 0:
            # Simple linear interpolation for NaN values
            nan_indices = np.where(nan_mask)[0]
            for idx in nan_indices:
                # Find nearest valid values before and after
                before = valid_indices[valid_indices < idx]
                after = valid_indices[valid_indices > idx]
                
                if len(before) > 0 and len(after) > 0:
                    before_idx = before[-1]
                    after_idx = after[0]
                    before_val = data[before_idx]
                    after_val = data[after_idx]
                    # Linear interpolation
                    filtered_data[idx] = before_val + (after_val - before_val) * (idx - before_idx) / (after_idx - before_idx)
                elif len(before) > 0:
                    filtered_data[idx] = data[before[-1]]
                elif len(after) > 0:
                    filtered_data[idx] = data[after[0]]
    
    # Apply Wiener filter to non-NaN values
    filtered_data[~nan_mask] = wiener(filtered_data[~nan_mask], window_size)
    
    # Restore NaN values in the original positions
    filtered_data[nan_mask] = np.nan
    
    return filtered_data

if __name__ == "__main__":
    resave_images_without_icc()
