import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import time
import numpy as np
import pandas as pd
import cv2
from scipy.ndimage import gaussian_filter
from tools.proc import fixation_detection_dispersion


def load_image_for_marker(marker):
    """
    Lädt das passende Bild für einen gegebenen Markerwert basierend auf den Codes.
    """
    scenario = {"1": 'a', "2": 'b', "3": 'c', "4": 'd', "5": 'e', "6": 'f'}
    ti_type = {"1": "system_goal", "2": "system_type", "3": "data", "4": "control"}

    marker_str = str(marker).zfill(3)  # Si<PERSON><PERSON>len, dass der Marker dreistellig ist
    scenario_key, ti_key, page = marker_str[0], marker_str[1], marker_str[2]

    if scenario_key in scenario and ti_key in ti_type:
        image_filename = f"database/ti_png/{scenario[scenario_key]}_{ti_type[ti_key]}_{page}.png"
        if os.path.exists(image_filename):
            return cv2.imread(image_filename)
    return cv2.imread(f"database/ti_png/default.png")


def generate_dynamic_heatmap(x, y, width, height, bin_size=3, sigma=20, max_frames=100):
    """
    Erstellt eine Heatmap basierend auf den letzten max_frames Blickpunkten, die sich nach und nach aufbaut
    """
    y_shape = height // bin_size
    x_shape = width // bin_size
    heatmap = np.zeros((y_shape, x_shape))

    recent_x = x[-max_frames:] if len(x) > max_frames else x
    recent_y = y[-max_frames:] if len(y) > max_frames else y

    for i in range(len(recent_x)):
        if np.isnan(recent_x[i]) or np.isnan(recent_y[i]):  # NaN-Werte überspringen
            continue
        x_idx = min(max(int(recent_x[i] // bin_size), 0), x_shape - 1)
        y_idx = min(max(int(recent_y[i] // bin_size), 0), y_shape - 1)
        heatmap[y_idx, x_idx] += 1

    heatmap = gaussian_filter(heatmap, sigma=sigma)
    if np.max(heatmap) > 0:
        heatmap = heatmap / np.max(heatmap)
    heatmap = cv2.resize(heatmap, (width, height))
    heatmap = (heatmap * 255).astype(np.uint8)
    heatmap = cv2.applyColorMap(heatmap, cv2.COLORMAP_JET)  # Reds-Colormap
    return heatmap


d_path = "database/raw_eyetracking/subj-01_group-1_role-Operator_eye_tracking_20241210094555.csv"
df = pd.read_csv(d_path, sep=";")

x = df["Screen_X_filtered"].to_numpy()
y = df["Screen_Y_filtered"].to_numpy()
timestamp = df["Timestamp"].to_numpy()
markers = df["Marker"].to_numpy()

print("Dataframe loaded")
fixations_idx = fixation_detection_dispersion(x, y, timestamp, d_threshold=20, duration_threshold=0.1)
fixations = np.zeros_like(x, dtype=np.uint8)
print("Dispersion Check finished")
for start, end in fixations_idx:
    fixations[start:end] = 1

height, width = 1200, 1920
canvas = np.zeros((height, width), dtype=np.uint8)
# Konvertiere das Canvas in ein 3-Kanal-Bild
canvas_bgr = cv2.cvtColor(canvas, cv2.COLOR_GRAY2BGR)
last_fix_coord_x, last_fix_coord_y = 0, 0
last_fix = -1

for frame in range(len(x)):
    canvas.fill(0)
    print(frame)
    image_overlay = load_image_for_marker(markers[frame])
    if image_overlay is not None:
        image_overlay = cv2.resize(image_overlay, (width, height))
        canvas = cv2.addWeighted(canvas_bgr, 0.0, image_overlay, 1.0, 0)

    dynamic_heatmap = generate_dynamic_heatmap(x[:frame + 1], y[:frame + 1], width, height)  # Dynamische Heatmap
    blended = cv2.addWeighted(canvas, 0.9, dynamic_heatmap, 0.1, 0)  # Heatmap hinzufügen

    if np.isnan(y[frame]) or np.isnan(x[frame]):
        continue

    cv2.circle(blended, (int(last_fix_coord_x), int(last_fix_coord_y)), radius=20, color=50, thickness=-1)

    if fixations[frame]:
        cv2.circle(blended, (int(x[frame]), int(y[frame])), radius=30, color=0, thickness=5)
        cv2.circle(blended, (int(x[frame]), int(y[frame])), radius=20, color=(0, 0, 255), thickness=-1)
    else:
        cv2.circle(blended, (int(x[frame]), int(y[frame])), radius=30, color=0, thickness=5)
        if last_fix == 1:
            last_fix_coord_x, last_fix_coord_y = x[frame], y[frame]

    last_fix = fixations[frame]

    cv2.imshow("Real-Time Gaze Point", blended)
    if cv2.waitKey(4) & 0xFF == ord('q'):
        break
