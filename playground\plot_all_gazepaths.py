import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from tools.proc import fixation_detection_velocity, load_image_for_marker, calculate_velocity

def plot_subject_gazepath(filepath, output_dir):
    """Plot gaze paths and fixations for a single subject."""
    # Extract subject ID from filename
    subject_id = os.path.basename(filepath).split('_')[0]
    print(subject_id)

    # Create subject-specific output directory
    subject_dir = os.path.join(output_dir, f"{subject_id}")
    os.makedirs(subject_dir, exist_ok=True)
    
    # Read data
    try:
        df = pd.read_csv(filepath, sep=";")
    except:
        print(f"File {filepath} is empty")
        return

    # Get unique markers for this subject
    markers = df['Marker'].unique()
    
    for marker in markers:
        # Skip markers below 100 (usually calibration markers)
        if marker < 100:
            continue
            
        # Filter data for current marker
        marker_data = df[df['Marker'] == marker]
        
        # Get necessary data
        x = marker_data["Screen_X_filtered"].to_numpy()
        y = marker_data["Screen_Y_filtered"].to_numpy()
        t = marker_data["Timestamp"].to_numpy()
        
        # calculate velocities
        velocities = calculate_velocity(x, y, t)
        # Calculate fixations
        fixations = fixation_detection_velocity(velocities, t, v_threshold=500)
        
        # Load background image
        image = load_image_for_marker(marker)
        if image is None:
            print(f"No image found for marker {marker}")
            continue
        
        # Create plot
        plt.figure(figsize=(19.2, 12), dpi=100)
        plt.subplots_adjust(left=0, right=1, top=1, bottom=0)
        plt.axis('off')
        
        # Plot background
        plt.imshow(image)
        
        # Plot gaze path
        plt.plot(x, y, 'black', alpha=0.3, linewidth=0.5, label='Gaze path')
        
        # Plot fixations
        for start, end in fixations:
            fix_x = np.mean(x[start:end])
            fix_y = np.mean(y[start:end])
            plt.plot(fix_x, fix_y, 'o', color='blue', alpha=0.7, markersize=8)
        
        # Add legend
        plt.legend(loc='upper right')
        
        # Save the plot
        plt.savefig(
            os.path.join(subject_dir, f"marker_{marker}_gazepath.png"),
            bbox_inches='tight',
            pad_inches=0,
            dpi=100
        )
        plt.close()

def main():
    # Define directories
    data_dir = "database/raw_eyetracking"
    output_dir = "results/gazepaths"
    
    # Create main output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Process all files
    for filename in os.listdir(data_dir):
        if not filename.endswith('.csv'):
            continue
            
        filepath = os.path.join(data_dir, filename)
        print(f"Processing {filename}...")
        plot_subject_gazepath(filepath, output_dir)
        print(f"Completed processing {filename}")

if __name__ == "__main__":
    main()