import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import numpy as np
import pandas as pd
import cv2
from tools.proc import fixation_detection_velocity
import time


def main():
    d_path = "database/raw_eyetracking/subj-01_group-1_role-Operator_eye_tracking_20241210094555.csv"
    df = pd.read_csv(d_path, sep=";")

    x = df["Screen_X_filtered"].to_numpy()
    y = df["Screen_Y_filtered"].to_numpy()
    timestamp = df["Timestamp"].to_numpy()

    fixations_idx = fixation_detection_velocity(x, y, timestamp, 1000, 0.1)
    fixations = np.zeros(x.shape)

    for fi in fixations_idx:
        fixations[fi[0]:fi[1]] = 1

    height = 1200
    width = 1920
    canvas = np.zeros((height, width), dtype=np.uint8)
    last_fix_coord_x = 0
    last_fix_coord_y = 0
    last_fix = -1

    for frame in range(len(x)):
        canvas[:] = 0
        cv2.circle(canvas, (int(last_fix_coord_y), int(last_fix_coord_x)), radius=30, color=50, thickness=-1)

        if np.isnan(y[frame]) or np.isnan(x[frame]):
            continue

        if fixations[frame] == 1:
            cv2.circle(canvas, (int(y[frame]), int(x[frame])), radius=30, color=255, thickness=5)
        else:
            if last_fix == 1:
                last_fix_coord_x = x[frame]
                last_fix_coord_y = y[frame]

        last_fix = fixations[frame]

        cv2.circle(canvas, (int(y[frame]), int(x[frame])), radius=20, color=255, thickness=-1)

        cv2.imshow("Real-Time Gaze Point", canvas)

        time.sleep(1 / 250)

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break


if __name__ == '__main__':
    main()
